"""
请求追踪中间件
为每个请求生成唯一ID，支持主从节点间的链路追踪
"""

import time
import uuid
from collections.abc import Callable
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from core.logging.logging_system import log as logger


class RequestIdMiddleware(BaseHTTPMiddleware):
    """
    请求ID中间件
    为每个请求生成或传递唯一的请求ID
    """

    def __init__(self, app, header_name: str = "X-Request-ID"):
        """
        初始化请求ID中间件

        Args:
            app: FastAPI应用实例
            header_name: 请求ID的HTTP头名称
        """
        super().__init__(app)
        self.header_name = header_name

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，生成或传递请求ID

        Args:
            request: HTTP请求
            call_next: 下一个中间件或处理器

        Returns:
            Response: HTTP响应
        """
        # 从请求头获取现有的请求ID，或生成新的
        request_id = request.headers.get(self.header_name)
        if not request_id:
            request_id = self._generate_request_id()

        # 将请求ID存储到请求状态中
        request.state.request_id = request_id

        # 记录请求开始
        start_time = time.time()
        logger.debug(f"Request started: {request_id} - {request.method} {request.url.path}")

        try:
            # 处理请求
            response = await call_next(request)

            # 在响应头中添加请求ID
            response.headers[self.header_name] = request_id

            # 记录请求完成
            duration = time.time() - start_time
            logger.debug(f"Request completed: {request_id} - {response.status_code} in {duration:.4f}s")

            return response

        except Exception as e:
            # 记录请求异常
            duration = time.time() - start_time
            # 安全地处理异常字符串转换
            try:
                error_msg = str(e)
            except Exception:
                error_msg = f"{type(e).__name__}: <字符串转换失败>"
            logger.error(f"Request failed: {request_id} - {error_msg} in {duration:.4f}s")
            raise

    def _generate_request_id(self) -> str:
        """
        生成唯一的请求ID

        Returns:
            str: 请求ID
        """
        return str(uuid.uuid4())


class RequestEvent:
    """请求事件"""

    def __init__(self, event_type: str, details: dict[str, Any] | None = None):
        self.timestamp = time.time()
        self.event_type = event_type
        self.details = details or {}


class RequestChain:
    """请求链路"""

    def __init__(self, request_id: str, endpoint: str, source: str):
        self.request_id = request_id
        self.endpoint = endpoint
        self.source = source  # master/slave
        self.start_time = time.time()
        self.events: list[RequestEvent] = []
        self.metadata: dict[str, Any] = {}

    def add_event(self, event_type: str, details: dict[str, Any] | None = None):
        """添加事件到请求链路"""
        event = RequestEvent(event_type, details)
        self.events.append(event)

    def set_metadata(self, key: str, value: Any):
        """设置元数据"""
        self.metadata[key] = value

    def get_duration(self) -> float:
        """获取请求持续时间"""
        return time.time() - self.start_time

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "request_id": self.request_id,
            "endpoint": self.endpoint,
            "source": self.source,
            "start_time": self.start_time,
            "duration": self.get_duration(),
            "events": [
                {"timestamp": event.timestamp, "event_type": event.event_type, "details": event.details}
                for event in self.events
            ],
            "metadata": self.metadata,
        }


class RequestTracker:
    """
    请求链路追踪器
    跟踪请求在主从节点间的流转和处理过程
    """

    def __init__(self, max_chains: int = 10000, cleanup_interval: int = 3600):
        """
        初始化请求追踪器

        Args:
            max_chains: 最大保存的请求链路数量
            cleanup_interval: 清理间隔（秒）
        """
        self.max_chains = max_chains
        self.cleanup_interval = cleanup_interval
        self.request_chains: dict[str, RequestChain] = {}
        self.last_cleanup = time.time()

        # 统计信息
        self.stats = {"total_requests": 0, "active_requests": 0, "completed_requests": 0, "failed_requests": 0}

    def start_request(self, request_id: str, endpoint: str, source: str) -> RequestChain:
        """
        开始请求追踪

        Args:
            request_id: 请求ID
            endpoint: 端点路径
            source: 请求来源（master/slave）

        Returns:
            RequestChain: 请求链路对象
        """
        # 清理过期的请求链路
        self._cleanup_if_needed()

        # 创建新的请求链路
        chain = RequestChain(request_id, endpoint, source)
        self.request_chains[request_id] = chain

        # 更新统计
        self.stats["total_requests"] += 1
        self.stats["active_requests"] += 1

        logger.debug(f"Started tracking request: {request_id} from {source}")
        return chain

    def add_event(self, request_id: str, event_type: str, details: dict[str, Any] | None = None):
        """
        添加请求事件

        Args:
            request_id: 请求ID
            event_type: 事件类型
            details: 事件详情
        """
        chain = self.request_chains.get(request_id)
        if chain:
            chain.add_event(event_type, details)
            logger.debug(f"Added event to request {request_id}: {event_type}")

    def complete_request(self, request_id: str, success: bool = True):
        """
        完成请求追踪

        Args:
            request_id: 请求ID
            success: 是否成功
        """
        chain = self.request_chains.get(request_id)
        if chain:
            event_type = "request_completed" if success else "request_failed"
            chain.add_event(event_type)

            # 更新统计
            self.stats["active_requests"] -= 1
            if success:
                self.stats["completed_requests"] += 1
            else:
                self.stats["failed_requests"] += 1

            logger.debug(f"Completed tracking request: {request_id} (success: {success})")

    def get_chain(self, request_id: str) -> dict[str, Any] | None:
        """
        获取请求链路信息

        Args:
            request_id: 请求ID

        Returns:
            Dict: 请求链路信息，如果不存在返回None
        """
        chain = self.request_chains.get(request_id)
        return chain.to_dict() if chain else None

    def get_active_chains(self) -> list[dict[str, Any]]:
        """
        获取所有活跃的请求链路

        Returns:
            List[Dict]: 活跃请求链路列表
        """
        current_time = time.time()
        active_chains = []

        for chain in self.request_chains.values():
            # 认为超过5分钟的请求为非活跃
            if current_time - chain.start_time < 300:
                active_chains.append(chain.to_dict())

        return active_chains

    def get_stats(self) -> dict[str, Any]:
        """
        获取追踪统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            "total_chains": len(self.request_chains),
            "memory_usage_mb": self._estimate_memory_usage(),
        }

    def _cleanup_if_needed(self):
        """根据需要清理过期的请求链路"""
        current_time = time.time()

        # 检查是否需要清理
        if current_time - self.last_cleanup < self.cleanup_interval:
            return

        # 清理超过1小时的请求链路
        cutoff_time = current_time - 3600
        expired_ids = [
            request_id for request_id, chain in self.request_chains.items() if chain.start_time < cutoff_time
        ]

        for request_id in expired_ids:
            del self.request_chains[request_id]

        # 如果仍然超过最大数量，删除最旧的
        if len(self.request_chains) > self.max_chains:
            sorted_chains = sorted(self.request_chains.items(), key=lambda x: x[1].start_time)

            excess_count = len(self.request_chains) - self.max_chains
            for request_id, _ in sorted_chains[:excess_count]:
                del self.request_chains[request_id]

        self.last_cleanup = current_time

        if expired_ids:
            logger.debug(f"Cleaned up {len(expired_ids)} expired request chains")

    def _estimate_memory_usage(self) -> float:
        """
        估算内存使用量（MB）

        Returns:
            float: 估算的内存使用量
        """
        # 粗略估算：每个请求链路约1KB
        return len(self.request_chains) * 1024 / (1024 * 1024)


# 全局请求追踪器实例
request_tracker = RequestTracker()
