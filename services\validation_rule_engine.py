"""
统一校验规则引擎
基于元数据驱动的校验规则处理核心引擎
支持动态规则加载、缓存优化和扩展性设计
"""

import json
import re
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleFieldMetadata
from services.rule_detail_service import ServiceError


class ValidationRuleType(Enum):
    """校验规则类型枚举"""

    REQUIRED = "required"
    MAX_LENGTH = "max_length"
    MIN_LENGTH = "min_length"
    MIN_VALUE = "min_value"
    MAX_VALUE = "max_value"
    PATTERN = "pattern"
    EMAIL = "email"
    DATE = "date"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ARRAY = "array"
    ENUM = "enum"
    CUSTOM = "custom"


@dataclass
class ValidationRule:
    """校验规则定义"""

    field_name: str
    chinese_name: str
    rule_type: ValidationRuleType
    rule_value: Any
    error_message: str
    warning_message: str | None = None
    is_required: bool = False
    priority: int = 0  # 优先级，数字越小优先级越高


@dataclass
class ValidationError:
    """校验错误定义"""

    field_name: str
    chinese_name: str
    error_code: str
    error_message: str
    error_value: Any
    rule_type: str
    suggestions: list[str] = None

    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


@dataclass
class ValidationResult:
    """校验结果"""

    valid: bool
    errors: list[ValidationError]
    warnings: list[str]
    field_count: int
    validated_fields: list[str]
    duration: float
    metadata: dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "valid": self.valid,
            "errors": [
                {
                    "field_name": error.field_name,
                    "chinese_name": error.chinese_name,
                    "error_code": error.error_code,
                    "error_message": error.error_message,
                    "error_value": error.error_value,
                    "rule_type": error.rule_type,
                    "suggestions": error.suggestions,
                }
                for error in self.errors
            ],
            "warnings": self.warnings,
            "field_count": self.field_count,
            "validated_fields": self.validated_fields,
            "duration": self.duration,
            "metadata": self.metadata,
        }


class ValidationRuleEngine:
    """统一校验规则引擎"""

    def __init__(self, session: Session):
        """
        初始化校验引擎

        Args:
            session: 数据库会话
        """
        self.session = session
        self._rule_cache = {}  # 规则缓存
        self._metadata_cache = {}  # 元数据缓存
        self._cache_timestamp = {}  # 缓存时间戳
        self.cache_ttl = 300  # 缓存TTL（秒）

    def get_validation_rules(self, rule_key: str, force_reload: bool = False) -> list[ValidationRule]:
        """
        获取指定规则模板的校验规则

        Args:
            rule_key: 规则模板键
            force_reload: 是否强制重新加载

        Returns:
            List[ValidationRule]: 校验规则列表

        Raises:
            ServiceError: 当获取规则失败时
        """
        cache_key = f"rules_{rule_key}"

        # 检查缓存
        if not force_reload and self._is_cache_valid(cache_key):
            return self._rule_cache[cache_key]

        try:
            # 从数据库获取字段元数据
            field_metadata_list = (
                self.session.query(RuleFieldMetadata)
                .filter(RuleFieldMetadata.rule_key == rule_key)
                .order_by(RuleFieldMetadata.excel_column_order.asc())
                .all()
            )

            if not field_metadata_list:
                logger.warning(f"未找到规则模板 '{rule_key}' 的字段元数据")
                return []

            # 转换为校验规则
            validation_rules = []
            for metadata in field_metadata_list:
                rules = self._parse_field_metadata_to_rules(metadata)
                validation_rules.extend(rules)

            # 按优先级排序
            validation_rules.sort(key=lambda x: x.priority)

            # 更新缓存
            self._rule_cache[cache_key] = validation_rules
            self._cache_timestamp[cache_key] = time.time()

            logger.info(f"成功加载规则模板 '{rule_key}' 的 {len(validation_rules)} 个校验规则")
            return validation_rules

        except Exception as e:
            error_msg = f"获取校验规则失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="VALIDATION_RULES_LOAD_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def validate_data(self, rule_key: str, data: dict[str, Any]) -> ValidationResult:
        """
        验证数据

        Args:
            rule_key: 规则模板键
            data: 待验证的数据

        Returns:
            ValidationResult: 验证结果
        """
        start_time = time.time()
        errors = []
        warnings = []
        validated_fields = []

        try:
            # 获取校验规则
            validation_rules = self.get_validation_rules(rule_key)

            if not validation_rules:
                logger.warning(f"规则模板 '{rule_key}' 没有配置校验规则")
                return ValidationResult(
                    valid=True,
                    errors=[],
                    warnings=["没有配置校验规则"],
                    field_count=len(data),
                    validated_fields=list(data.keys()),
                    duration=time.time() - start_time,
                )

            # 按字段分组规则
            field_rules = {}
            for rule in validation_rules:
                if rule.field_name not in field_rules:
                    field_rules[rule.field_name] = []
                field_rules[rule.field_name].append(rule)

            # 验证每个字段
            for field_name, rules in field_rules.items():
                field_value = data.get(field_name)
                validated_fields.append(field_name)

                for rule in rules:
                    error = self._validate_field_by_rule(field_value, rule)
                    if error:
                        if error.error_code.startswith("WARNING"):
                            warnings.append(error.error_message)
                        else:
                            errors.append(error)

            duration = time.time() - start_time

            result = ValidationResult(
                valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                field_count=len(data),
                validated_fields=validated_fields,
                duration=duration,
                metadata={
                    "rule_key": rule_key,
                    "total_rules": len(validation_rules),
                    "validated_at": datetime.now().isoformat(),
                },
            )

            logger.info(
                f"数据校验完成: rule_key={rule_key}, valid={result.valid}, "
                f"errors={len(errors)}, warnings={len(warnings)}, duration={duration:.3f}s"
            )

            return result

        except Exception as e:
            error_msg = f"数据校验失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="DATA_VALIDATION_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def _parse_field_metadata_to_rules(self, metadata: RuleFieldMetadata) -> list[ValidationRule]:
        """
        将字段元数据转换为校验规则

        Args:
            metadata: 字段元数据

        Returns:
            List[ValidationRule]: 校验规则列表
        """
        rules = []

        try:
            # 解析验证规则JSON
            validation_rules_json = metadata.validation_rule or "[]"
            validation_rules_data = json.loads(validation_rules_json) if validation_rules_json else []

            # 必填字段规则
            if metadata.is_required:
                rules.append(
                    ValidationRule(
                        field_name=metadata.field_name,
                        chinese_name=metadata.display_name,
                        rule_type=ValidationRuleType.REQUIRED,
                        rule_value=True,
                        error_message=f"{metadata.display_name}不能为空",
                        is_required=True,
                        priority=0,  # 必填校验优先级最高
                    )
                )

            # 其他校验规则
            for rule_data in validation_rules_data:
                if isinstance(rule_data, str):
                    # 字符串格式规则，如 "max_length:100"
                    rule = self._parse_string_rule(rule_data, metadata)
                    if rule:
                        rules.append(rule)
                elif isinstance(rule_data, dict):
                    # 字典格式规则
                    rule = self._parse_dict_rule(rule_data, metadata)
                    if rule:
                        rules.append(rule)

        except Exception as e:
            logger.warning(f"解析字段 '{metadata.field_name}' 的校验规则失败: {str(e)}")

        return rules

    def _parse_string_rule(self, rule_str: str, metadata: RuleFieldMetadata) -> ValidationRule | None:
        """解析字符串格式的校验规则"""
        try:
            if ":" in rule_str:
                rule_type_str, rule_value_str = rule_str.split(":", 1)
            else:
                rule_type_str = rule_str
                rule_value_str = "true"

            rule_type_str = rule_type_str.strip()
            rule_value_str = rule_value_str.strip()

            # 转换规则类型
            rule_type = ValidationRuleType(rule_type_str)

            # 解析规则值
            if rule_type in [
                ValidationRuleType.MAX_LENGTH,
                ValidationRuleType.MIN_LENGTH,
                ValidationRuleType.MIN_VALUE,
                ValidationRuleType.MAX_VALUE,
            ]:
                rule_value = int(rule_value_str)
            elif rule_type == ValidationRuleType.BOOLEAN:
                rule_value = rule_value_str.lower() in ["true", "1", "yes"]
            elif rule_type == ValidationRuleType.ARRAY:
                rule_value = json.loads(rule_value_str) if rule_value_str.startswith("[") else rule_value_str.split(",")
            else:
                rule_value = rule_value_str

            # 生成错误信息
            error_message = self._generate_error_message(rule_type, rule_value, metadata.display_name)

            return ValidationRule(
                field_name=metadata.field_name,
                chinese_name=metadata.display_name,
                rule_type=rule_type,
                rule_value=rule_value,
                error_message=error_message,
                priority=self._get_rule_priority(rule_type),
            )

        except (ValueError, json.JSONDecodeError) as e:
            logger.warning(f"解析规则字符串 '{rule_str}' 失败: {str(e)}")
            return None

    def _parse_dict_rule(self, rule_dict: dict[str, Any], metadata: RuleFieldMetadata) -> ValidationRule | None:
        """解析字典格式的校验规则"""
        try:
            rule_type = ValidationRuleType(rule_dict.get("type"))
            rule_value = rule_dict.get("value")
            error_message = rule_dict.get("message") or self._generate_error_message(
                rule_type, rule_value, metadata.display_name
            )
            warning_message = rule_dict.get("warning")
            priority = rule_dict.get("priority", self._get_rule_priority(rule_type))

            return ValidationRule(
                field_name=metadata.field_name,
                chinese_name=metadata.display_name,
                rule_type=rule_type,
                rule_value=rule_value,
                error_message=error_message,
                warning_message=warning_message,
                priority=priority,
            )

        except (ValueError, KeyError) as e:
            logger.warning(f"解析规则字典失败: {str(e)}")
            return None

    def _validate_field_by_rule(self, field_value: Any, rule: ValidationRule) -> ValidationError | None:
        """
        根据规则验证字段值

        Args:
            field_value: 字段值
            rule: 校验规则

        Returns:
            Optional[ValidationError]: 校验错误，无错误时返回None
        """
        try:
            # 必填校验
            if rule.rule_type == ValidationRuleType.REQUIRED:
                if field_value is None or (isinstance(field_value, str) and field_value.strip() == ""):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="REQUIRED_FIELD_MISSING",
                        error_message=rule.error_message,
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请填写{rule.chinese_name}"],
                    )

            # 如果字段为空且不是必填，跳过其他校验
            if field_value is None or (isinstance(field_value, str) and field_value.strip() == ""):
                return None

            # 长度校验
            if rule.rule_type == ValidationRuleType.MAX_LENGTH:
                if isinstance(field_value, str) and len(field_value) > rule.rule_value:
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="MAX_LENGTH_EXCEEDED",
                        error_message=rule.error_message,
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请将{rule.chinese_name}长度控制在{rule.rule_value}个字符以内"],
                    )

            elif rule.rule_type == ValidationRuleType.MIN_LENGTH:
                if isinstance(field_value, str) and len(field_value) < rule.rule_value:
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="MIN_LENGTH_NOT_MET",
                        error_message=rule.error_message,
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"{rule.chinese_name}长度至少需要{rule.rule_value}个字符"],
                    )

            # 数值校验
            elif rule.rule_type == ValidationRuleType.MIN_VALUE:
                try:
                    numeric_value = float(field_value)
                    if numeric_value < rule.rule_value:
                        return ValidationError(
                            field_name=rule.field_name,
                            chinese_name=rule.chinese_name,
                            error_code="MIN_VALUE_NOT_MET",
                            error_message=rule.error_message,
                            error_value=field_value,
                            rule_type=rule.rule_type.value,
                            suggestions=[f"{rule.chinese_name}的值不能小于{rule.rule_value}"],
                        )
                except (ValueError, TypeError):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_NUMERIC_VALUE",
                        error_message=f"{rule.chinese_name}必须是有效的数字",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请输入有效的数字作为{rule.chinese_name}"],
                    )

            elif rule.rule_type == ValidationRuleType.MAX_VALUE:
                try:
                    numeric_value = float(field_value)
                    if numeric_value > rule.rule_value:
                        return ValidationError(
                            field_name=rule.field_name,
                            chinese_name=rule.chinese_name,
                            error_code="MAX_VALUE_EXCEEDED",
                            error_message=rule.error_message,
                            error_value=field_value,
                            rule_type=rule.rule_type.value,
                            suggestions=[f"{rule.chinese_name}的值不能大于{rule.rule_value}"],
                        )
                except (ValueError, TypeError):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_NUMERIC_VALUE",
                        error_message=f"{rule.chinese_name}必须是有效的数字",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请输入有效的数字作为{rule.chinese_name}"],
                    )

            # 类型校验
            elif rule.rule_type == ValidationRuleType.INTEGER:
                try:
                    int(field_value)
                except (ValueError, TypeError):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_INTEGER_VALUE",
                        error_message=f"{rule.chinese_name}必须是整数",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请输入整数作为{rule.chinese_name}"],
                    )

            elif rule.rule_type == ValidationRuleType.FLOAT:
                try:
                    float(field_value)
                except (ValueError, TypeError):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_FLOAT_VALUE",
                        error_message=f"{rule.chinese_name}必须是有效的小数",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请输入有效的小数作为{rule.chinese_name}"],
                    )

            # 正则表达式校验
            elif rule.rule_type == ValidationRuleType.PATTERN:
                if not re.match(rule.rule_value, str(field_value)):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="PATTERN_NOT_MATCHED",
                        error_message=rule.error_message,
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"{rule.chinese_name}格式不正确"],
                    )

            # 邮箱校验
            elif rule.rule_type == ValidationRuleType.EMAIL:
                email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                if not re.match(email_pattern, str(field_value)):
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_EMAIL_FORMAT",
                        error_message=f"{rule.chinese_name}格式不正确",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=["请输入有效的邮箱地址，如：<EMAIL>"],
                    )

            # 日期校验
            elif rule.rule_type == ValidationRuleType.DATE:
                try:
                    datetime.strptime(str(field_value), "%Y-%m-%d")
                except ValueError:
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_DATE_FORMAT",
                        error_message=f"{rule.chinese_name}日期格式不正确",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=["请使用YYYY-MM-DD格式，如：2025-01-25"],
                    )

            # 枚举校验
            elif rule.rule_type == ValidationRuleType.ENUM:
                if field_value not in rule.rule_value:
                    return ValidationError(
                        field_name=rule.field_name,
                        chinese_name=rule.chinese_name,
                        error_code="INVALID_ENUM_VALUE",
                        error_message=f"{rule.chinese_name}的值不在允许范围内",
                        error_value=field_value,
                        rule_type=rule.rule_type.value,
                        suggestions=[f"请选择以下值之一：{', '.join(map(str, rule.rule_value))}"],
                    )

            return None

        except Exception as e:
            logger.error(f"字段校验异常: field={rule.field_name}, rule={rule.rule_type.value}, error={str(e)}")
            return ValidationError(
                field_name=rule.field_name,
                chinese_name=rule.chinese_name,
                error_code="VALIDATION_EXCEPTION",
                error_message=f"{rule.chinese_name}校验时发生异常",
                error_value=field_value,
                rule_type=rule.rule_type.value,
                suggestions=["请检查数据格式或联系系统管理员"],
            )

    def _generate_error_message(self, rule_type: ValidationRuleType, rule_value: Any, field_name: str) -> str:
        """生成错误信息"""
        if rule_type == ValidationRuleType.REQUIRED:
            return f"{field_name}不能为空"
        elif rule_type == ValidationRuleType.MAX_LENGTH:
            return f"{field_name}长度不能超过{rule_value}个字符"
        elif rule_type == ValidationRuleType.MIN_LENGTH:
            return f"{field_name}长度不能少于{rule_value}个字符"
        elif rule_type == ValidationRuleType.MIN_VALUE:
            return f"{field_name}的值不能小于{rule_value}"
        elif rule_type == ValidationRuleType.MAX_VALUE:
            return f"{field_name}的值不能大于{rule_value}"
        elif rule_type == ValidationRuleType.PATTERN:
            return f"{field_name}格式不正确"
        elif rule_type == ValidationRuleType.EMAIL:
            return f"{field_name}邮箱格式不正确"
        elif rule_type == ValidationRuleType.DATE:
            return f"{field_name}日期格式不正确"
        elif rule_type == ValidationRuleType.INTEGER:
            return f"{field_name}必须是整数"
        elif rule_type == ValidationRuleType.FLOAT:
            return f"{field_name}必须是有效的小数"
        elif rule_type == ValidationRuleType.ENUM:
            return f"{field_name}的值不在允许范围内"
        else:
            return f"{field_name}校验失败"

    def _get_rule_priority(self, rule_type: ValidationRuleType) -> int:
        """获取规则优先级"""
        priority_map = {
            ValidationRuleType.REQUIRED: 0,
            ValidationRuleType.INTEGER: 1,
            ValidationRuleType.FLOAT: 1,
            ValidationRuleType.DATE: 1,
            ValidationRuleType.EMAIL: 1,
            ValidationRuleType.PATTERN: 2,
            ValidationRuleType.MIN_LENGTH: 3,
            ValidationRuleType.MAX_LENGTH: 3,
            ValidationRuleType.MIN_VALUE: 4,
            ValidationRuleType.MAX_VALUE: 4,
            ValidationRuleType.ENUM: 5,
            ValidationRuleType.CUSTOM: 10,
        }
        return priority_map.get(rule_type, 10)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_timestamp:
            return False

        return (time.time() - self._cache_timestamp[cache_key]) < self.cache_ttl

    def clear_cache(self, rule_key: str | None = None):
        """清除缓存"""
        if rule_key:
            cache_key = f"rules_{rule_key}"
            self._rule_cache.pop(cache_key, None)
            self._cache_timestamp.pop(cache_key, None)
        else:
            self._rule_cache.clear()
            self._cache_timestamp.clear()
            self._metadata_cache.clear()

    def get_cache_stats(self) -> dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "rule_cache_size": len(self._rule_cache),
            "metadata_cache_size": len(self._metadata_cache),
            "cache_ttl": self.cache_ttl,
            "cached_rule_keys": [key.replace("rules_", "") for key in self._rule_cache.keys()],
        }
