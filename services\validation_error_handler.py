"""
校验错误处理标准化模块
统一错误码管理、错误信息格式化和多语言支持
"""

import time
from dataclasses import dataclass
from enum import Enum
from typing import Any

from core.logging.logging_system import log as logger


class ValidationErrorCode(Enum):
    """校验错误码枚举"""
    # 必填字段错误
    REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING"

    # 长度校验错误
    MAX_LENGTH_EXCEEDED = "MAX_LENGTH_EXCEEDED"
    MIN_LENGTH_NOT_MET = "MIN_LENGTH_NOT_MET"

    # 数值校验错误
    MIN_VALUE_NOT_MET = "MIN_VALUE_NOT_MET"
    MAX_VALUE_EXCEEDED = "MAX_VALUE_EXCEEDED"
    INVALID_NUMERIC_VALUE = "INVALID_NUMERIC_VALUE"
    INVALID_INTEGER_VALUE = "INVALID_INTEGER_VALUE"
    INVALID_FLOAT_VALUE = "INVALID_FLOAT_VALUE"

    # 格式校验错误
    PATTERN_NOT_MATCHED = "PATTERN_NOT_MATCHED"
    INVALID_EMAIL_FORMAT = "INVALID_EMAIL_FORMAT"
    INVALID_DATE_FORMAT = "INVALID_DATE_FORMAT"

    # 枚举校验错误
    INVALID_ENUM_VALUE = "INVALID_ENUM_VALUE"

    # 系统错误
    VALIDATION_EXCEPTION = "VALIDATION_EXCEPTION"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"


@dataclass
class StandardizedError:
    """标准化错误"""
    field_name: str
    chinese_name: str
    error_code: ValidationErrorCode
    error_message: str
    error_value: Any
    rule_type: str
    suggestions: list[str]
    severity: str = "error"  # error, warning, info
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "field_name": self.field_name,
            "chinese_name": self.chinese_name,
            "error_code": self.error_code.value,
            "error_message": self.error_message,
            "error_value": self.error_value,
            "rule_type": self.rule_type,
            "suggestions": self.suggestions,
            "severity": self.severity,
            "timestamp": int(self.timestamp * 1000)  # 毫秒时间戳
        }

    def to_frontend_format(self) -> dict[str, Any]:
        """转换为前端格式"""
        return {
            "field": self.field_name,
            "fieldName": self.chinese_name,
            "code": self.error_code.value,
            "message": self.error_message,
            "value": self.error_value,
            "type": self.rule_type,
            "suggestions": self.suggestions,
            "severity": self.severity
        }


class ValidationErrorHandler:
    """校验错误处理器"""

    def __init__(self, language: str = "zh-CN"):
        """
        初始化错误处理器

        Args:
            language: 语言代码
        """
        self.language = language
        self._error_templates = self._load_error_templates()

    def standardize_error(
        self,
        field_name: str,
        chinese_name: str,
        error_code: str,
        error_value: Any = None,
        rule_type: str = "unknown",
        rule_value: Any = None,
        custom_message: str = None
    ) -> StandardizedError:
        """
        标准化错误

        Args:
            field_name: 字段名
            chinese_name: 中文名称
            error_code: 错误码
            error_value: 错误值
            rule_type: 规则类型
            rule_value: 规则值
            custom_message: 自定义错误信息

        Returns:
            StandardizedError: 标准化错误
        """
        try:
            # 转换错误码
            if isinstance(error_code, str):
                error_code_enum = ValidationErrorCode(error_code)
            else:
                error_code_enum = error_code

            # 生成错误信息
            if custom_message:
                error_message = custom_message
            else:
                error_message = self._generate_error_message(
                    error_code_enum, chinese_name, rule_value
                )

            # 生成建议
            suggestions = self._generate_suggestions(
                error_code_enum, chinese_name, rule_value
            )

            # 确定严重程度
            severity = self._get_error_severity(error_code_enum)

            return StandardizedError(
                field_name=field_name,
                chinese_name=chinese_name,
                error_code=error_code_enum,
                error_message=error_message,
                error_value=error_value,
                rule_type=rule_type,
                suggestions=suggestions,
                severity=severity
            )

        except Exception as e:
            logger.error(f"标准化错误失败: {str(e)}")
            # 返回通用错误
            return StandardizedError(
                field_name=field_name,
                chinese_name=chinese_name,
                error_code=ValidationErrorCode.UNKNOWN_ERROR,
                error_message=f"{chinese_name}校验失败",
                error_value=error_value,
                rule_type=rule_type,
                suggestions=["请检查数据格式或联系系统管理员"],
                severity="error"
            )

    def batch_standardize_errors(self, errors: list[dict[str, Any]]) -> list[StandardizedError]:
        """
        批量标准化错误

        Args:
            errors: 错误列表

        Returns:
            List[StandardizedError]: 标准化错误列表
        """
        standardized_errors = []

        for error in errors:
            try:
                if isinstance(error, dict):
                    standardized_error = self.standardize_error(
                        field_name=error.get("field_name", "unknown"),
                        chinese_name=error.get("chinese_name", "未知字段"),
                        error_code=error.get("error_code", "UNKNOWN_ERROR"),
                        error_value=error.get("error_value"),
                        rule_type=error.get("rule_type", "unknown"),
                        rule_value=error.get("rule_value"),
                        custom_message=error.get("error_message")
                    )
                    standardized_errors.append(standardized_error)
                elif hasattr(error, 'to_dict'):
                    # 如果已经是标准化错误对象
                    standardized_errors.append(error)
                else:
                    # 处理其他格式的错误
                    standardized_error = self.standardize_error(
                        field_name="unknown",
                        chinese_name="未知字段",
                        error_code="UNKNOWN_ERROR",
                        error_value=str(error),
                        custom_message=str(error)
                    )
                    standardized_errors.append(standardized_error)

            except Exception as e:
                logger.error(f"标准化单个错误失败: {str(e)}")
                # 创建通用错误
                fallback_error = StandardizedError(
                    field_name="system",
                    chinese_name="系统",
                    error_code=ValidationErrorCode.VALIDATION_EXCEPTION,
                    error_message=f"错误处理异常: {str(e)}",
                    error_value=None,
                    rule_type="system",
                    suggestions=["请联系系统管理员"]
                )
                standardized_errors.append(fallback_error)

        return standardized_errors

    def format_errors_for_api(self, errors: list[StandardizedError]) -> dict[str, Any]:
        """
        格式化错误用于API响应

        Args:
            errors: 标准化错误列表

        Returns:
            Dict: API响应格式
        """
        return {
            "code": "VALIDATION_FAILED",
            "success": False,
            "message": f"数据校验失败，发现{len(errors)}个错误",
            "data": {
                "errors": [error.to_dict() for error in errors],
                "error_count": len(errors),
                "error_summary": self._generate_error_summary(errors)
            }
        }

    def format_errors_for_frontend(self, errors: list[StandardizedError]) -> dict[str, Any]:
        """
        格式化错误用于前端显示

        Args:
            errors: 标准化错误列表

        Returns:
            Dict: 前端格式
        """
        return {
            "valid": False,
            "errors": [error.to_frontend_format() for error in errors],
            "summary": self._generate_user_friendly_summary(errors)
        }

    def _generate_error_message(
        self, 
        error_code: ValidationErrorCode, 
        field_name: str, 
        rule_value: Any = None
    ) -> str:
        """生成错误信息"""
        templates = self._error_templates.get(self.language, {})
        template = templates.get(error_code.value, f"{field_name}校验失败")

        # 替换模板变量
        if rule_value is not None:
            template = template.replace("{field_name}", field_name)
            template = template.replace("{rule_value}", str(rule_value))
        else:
            template = template.replace("{field_name}", field_name)

        return template

    def _generate_suggestions(
        self, 
        error_code: ValidationErrorCode, 
        field_name: str, 
        rule_value: Any = None
    ) -> list[str]:
        """生成建议"""
        suggestions_map = {
            ValidationErrorCode.REQUIRED_FIELD_MISSING: [f"请填写{field_name}"],
            ValidationErrorCode.MAX_LENGTH_EXCEEDED: [f"请将{field_name}长度控制在{rule_value}个字符以内"],
            ValidationErrorCode.MIN_LENGTH_NOT_MET: [f"{field_name}长度至少需要{rule_value}个字符"],
            ValidationErrorCode.MIN_VALUE_NOT_MET: [f"{field_name}的值不能小于{rule_value}"],
            ValidationErrorCode.MAX_VALUE_EXCEEDED: [f"{field_name}的值不能大于{rule_value}"],
            ValidationErrorCode.INVALID_NUMERIC_VALUE: [f"请输入有效的数字作为{field_name}"],
            ValidationErrorCode.INVALID_INTEGER_VALUE: [f"请输入整数作为{field_name}"],
            ValidationErrorCode.INVALID_FLOAT_VALUE: [f"请输入有效的小数作为{field_name}"],
            ValidationErrorCode.PATTERN_NOT_MATCHED: [f"{field_name}格式不正确"],
            ValidationErrorCode.INVALID_EMAIL_FORMAT: ["请输入有效的邮箱地址，如：<EMAIL>"],
            ValidationErrorCode.INVALID_DATE_FORMAT: ["请使用YYYY-MM-DD格式，如：2025-01-25"],
            ValidationErrorCode.VALIDATION_EXCEPTION: ["请检查数据格式或联系系统管理员"]
        }

        return suggestions_map.get(error_code, [f"请检查{field_name}的格式"])

    def _get_error_severity(self, error_code: ValidationErrorCode) -> str:
        """获取错误严重程度"""
        severity_map = {
            ValidationErrorCode.REQUIRED_FIELD_MISSING: "error",
            ValidationErrorCode.MAX_LENGTH_EXCEEDED: "error",
            ValidationErrorCode.MIN_LENGTH_NOT_MET: "error",
            ValidationErrorCode.MIN_VALUE_NOT_MET: "error",
            ValidationErrorCode.MAX_VALUE_EXCEEDED: "error",
            ValidationErrorCode.INVALID_NUMERIC_VALUE: "error",
            ValidationErrorCode.INVALID_INTEGER_VALUE: "error",
            ValidationErrorCode.INVALID_FLOAT_VALUE: "error",
            ValidationErrorCode.PATTERN_NOT_MATCHED: "error",
            ValidationErrorCode.INVALID_EMAIL_FORMAT: "error",
            ValidationErrorCode.INVALID_DATE_FORMAT: "error",
            ValidationErrorCode.INVALID_ENUM_VALUE: "error",
            ValidationErrorCode.VALIDATION_EXCEPTION: "error",
            ValidationErrorCode.UNKNOWN_ERROR: "error"
        }

        return severity_map.get(error_code, "error")

    def _generate_error_summary(self, errors: list[StandardizedError]) -> dict[str, Any]:
        """生成错误摘要"""
        error_types = {}
        field_errors = {}

        for error in errors:
            # 按错误类型统计
            error_type = error.error_code.value
            if error_type not in error_types:
                error_types[error_type] = 0
            error_types[error_type] += 1

            # 按字段统计
            field_name = error.field_name
            if field_name not in field_errors:
                field_errors[field_name] = []
            field_errors[field_name].append(error.error_message)

        return {
            "total_errors": len(errors),
            "error_types": error_types,
            "field_errors": field_errors,
            "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
        }

    def _generate_user_friendly_summary(self, errors: list[StandardizedError]) -> str:
        """生成用户友好的错误摘要"""
        if not errors:
            return "数据校验通过"

        if len(errors) == 1:
            return errors[0].error_message

        # 按字段分组
        field_groups = {}
        for error in errors:
            field_name = error.chinese_name
            if field_name not in field_groups:
                field_groups[field_name] = []
            field_groups[field_name].append(error.error_message)

        if len(field_groups) <= 3:
            # 字段较少时，列出具体字段
            field_messages = []
            for field_name, messages in field_groups.items():
                field_messages.append(f"{field_name}({len(messages)}个错误)")
            return f"发现{len(errors)}个校验错误：{', '.join(field_messages)}"
        else:
            # 字段较多时，给出总体描述
            return f"发现{len(errors)}个校验错误，涉及{len(field_groups)}个字段，请检查数据格式"

    def _load_error_templates(self) -> dict[str, dict[str, str]]:
        """加载错误信息模板"""
        return {
            "zh-CN": {
                "REQUIRED_FIELD_MISSING": "{field_name}不能为空",
                "MAX_LENGTH_EXCEEDED": "{field_name}长度不能超过{rule_value}个字符",
                "MIN_LENGTH_NOT_MET": "{field_name}长度不能少于{rule_value}个字符",
                "MIN_VALUE_NOT_MET": "{field_name}的值不能小于{rule_value}",
                "MAX_VALUE_EXCEEDED": "{field_name}的值不能大于{rule_value}",
                "INVALID_NUMERIC_VALUE": "{field_name}必须是有效的数字",
                "INVALID_INTEGER_VALUE": "{field_name}必须是整数",
                "INVALID_FLOAT_VALUE": "{field_name}必须是有效的小数",
                "PATTERN_NOT_MATCHED": "{field_name}格式不正确",
                "INVALID_EMAIL_FORMAT": "{field_name}邮箱格式不正确",
                "INVALID_DATE_FORMAT": "{field_name}日期格式不正确",
                "INVALID_ENUM_VALUE": "{field_name}的值不在允许范围内",
                "VALIDATION_EXCEPTION": "{field_name}校验时发生异常",
                "UNKNOWN_ERROR": "{field_name}校验失败"
            },
            "en-US": {
                "REQUIRED_FIELD_MISSING": "{field_name} is required",
                "MAX_LENGTH_EXCEEDED": "{field_name} cannot exceed {rule_value} characters",
                "MIN_LENGTH_NOT_MET": "{field_name} must be at least {rule_value} characters",
                "MIN_VALUE_NOT_MET": "{field_name} cannot be less than {rule_value}",
                "MAX_VALUE_EXCEEDED": "{field_name} cannot be greater than {rule_value}",
                "INVALID_NUMERIC_VALUE": "{field_name} must be a valid number",
                "INVALID_INTEGER_VALUE": "{field_name} must be an integer",
                "INVALID_FLOAT_VALUE": "{field_name} must be a valid decimal",
                "PATTERN_NOT_MATCHED": "{field_name} format is incorrect",
                "INVALID_EMAIL_FORMAT": "{field_name} email format is incorrect",
                "INVALID_DATE_FORMAT": "{field_name} date format is incorrect",
                "INVALID_ENUM_VALUE": "{field_name} value is not in allowed range",
                "VALIDATION_EXCEPTION": "Exception occurred while validating {field_name}",
                "UNKNOWN_ERROR": "{field_name} validation failed"
            }
        }
