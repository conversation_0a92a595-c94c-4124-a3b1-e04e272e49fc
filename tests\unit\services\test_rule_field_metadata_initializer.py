"""
字段元数据初始化服务单元测试
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from models.database import Base, FieldTypeEnum, RuleFieldMetadata, RuleTemplate, RuleTemplateStatusEnum
from services.rule_field_metadata_initializer import (
    FieldMetadataBuilder,
    InitializationStats,
    RuleFieldMetadataInitializer,
    RuleFieldMetadataInitializerError,
    ValidationEngine,
)
from tools.field_mapping_manager import FieldMappingManager


@pytest.mark.unit
@pytest.mark.field_metadata
class TestInitializationStats:
    """初始化统计信息测试类"""

    def test_initialization_stats_creation(self):
        """测试统计信息创建"""
        stats = InitializationStats()

        assert stats.total_rule_types == 0
        assert stats.created_templates == 0
        assert stats.updated_templates == 0
        assert stats.created_field_metadata == 0
        assert stats.updated_field_metadata == 0
        assert stats.errors == []
        assert stats.warnings == []
        assert stats.start_time is not None
        assert stats.end_time is None

    def test_finalize_stats(self):
        """测试统计信息完成"""
        stats = InitializationStats()
        stats.finalize()

        assert stats.end_time is not None
        assert stats.duration >= 0

    def test_to_dict(self):
        """测试转换为字典"""
        stats = InitializationStats()
        stats.total_rule_types = 5
        stats.created_templates = 3
        stats.errors = ["error1", "error2"]
        stats.finalize()

        result = stats.to_dict()

        assert result["total_rule_types"] == 5
        assert result["created_templates"] == 3
        assert result["total_errors"] == 2
        assert "start_time" in result
        assert "end_time" in result
        assert "duration_seconds" in result


@pytest.mark.unit
@pytest.mark.field_metadata
class TestFieldMetadataBuilder:
    """字段元数据构建器测试类"""

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "version": "3.1.0",
            "rule_type_mappings": {
                "test_rule": {
                    "name": "测试规则",
                    "required_fields": ["rule_name", "level1"],
                    "optional_fields": ["description"],
                }
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {"chinese_name": "规则名称", "data_type": "string", "description": "规则的显示名称"},
                    "level1": {"chinese_name": "一级错误类型", "data_type": "string", "description": "一级错误类型"},
                },
                "specific_fields": {
                    "description": {"chinese_name": "描述", "data_type": "text", "description": "详细描述"}
                },
            },
        }

    @pytest.fixture
    def mock_field_mapping_manager(self, sample_config):
        """模拟字段映射管理器"""
        manager = Mock(spec=FieldMappingManager)
        manager.config = sample_config
        manager.get_field_definition.side_effect = lambda field_name: sample_config["field_definitions"][
            "common_fields"
        ].get(field_name, sample_config["field_definitions"]["specific_fields"].get(field_name))
        return manager

    @pytest.fixture
    def field_metadata_builder(self, mock_field_mapping_manager):
        """字段元数据构建器实例"""
        return FieldMetadataBuilder(mock_field_mapping_manager)

    def test_build_template_record(self, field_metadata_builder):
        """测试构建规则模板记录"""
        rule_key = "test_rule"
        rule_info = {"name": "测试规则", "description": "测试描述"}

        template = field_metadata_builder.build_template_record(rule_key, rule_info)

        assert isinstance(template, RuleTemplate)
        assert template.rule_key == "test_rule"
        assert template.rule_type == "test_rule"
        assert template.name == "测试规则"
        assert template.status == RuleTemplateStatusEnum.NEW

    def test_build_template_record_invalid_input(self, field_metadata_builder):
        """测试构建规则模板记录 - 无效输入"""
        with pytest.raises(ValueError, match="规则键值不能为空"):
            field_metadata_builder.build_template_record("", {})

        with pytest.raises(ValueError, match="规则信息必须是字典类型"):
            field_metadata_builder.build_template_record("test", "invalid")

    def test_build_field_metadata_records(self, field_metadata_builder):
        """测试构建字段元数据记录列表"""
        rule_key = "test_rule"
        field_list = ["rule_name", "level1"]
        required_fields = ["rule_name"]

        records = field_metadata_builder.build_field_metadata_records(rule_key, field_list, required_fields)

        assert len(records) == 2
        assert all(isinstance(record, RuleFieldMetadata) for record in records)
        assert records[0].rule_key == "test_rule"
        assert records[0].is_required == True  # rule_name is required
        assert records[1].is_required == False  # level1 is not required

    def test_build_field_metadata_records_empty_list(self, field_metadata_builder):
        """测试构建字段元数据记录 - 空字段列表"""
        records = field_metadata_builder.build_field_metadata_records("test_rule", [])
        assert records == []

    def test_determine_field_properties(self, field_metadata_builder):
        """测试确定字段属性"""
        field_name = "rule_name"
        field_def = {"chinese_name": "规则名称", "data_type": "string", "description": "规则的显示名称"}

        properties = field_metadata_builder._determine_field_properties(field_name, field_def)

        assert properties["field_type"] == FieldTypeEnum.STRING
        assert properties["is_fixed_field"] == True  # rule_name is in common_fields
        assert "excel_order" in properties

    def test_map_field_type(self, field_metadata_builder):
        """测试映射字段类型"""
        assert field_metadata_builder._map_field_type("string") == FieldTypeEnum.STRING
        assert field_metadata_builder._map_field_type("integer") == FieldTypeEnum.INTEGER
        assert field_metadata_builder._map_field_type("array") == FieldTypeEnum.ARRAY
        assert field_metadata_builder._map_field_type("boolean") == FieldTypeEnum.BOOLEAN
        assert field_metadata_builder._map_field_type("unknown") == FieldTypeEnum.STRING

    def test_is_fixed_field(self, field_metadata_builder):
        """测试判断固定字段"""
        assert field_metadata_builder._is_fixed_field("rule_name") == True
        assert field_metadata_builder._is_fixed_field("description") == False

    def test_calculate_excel_order(self, field_metadata_builder):
        """测试计算Excel列顺序"""
        # 测试通用字段
        order1 = field_metadata_builder._calculate_excel_order("rule_name", True, {})
        assert order1 == 1  # rule_name是第一个common_field

        # 测试特定字段
        order2 = field_metadata_builder._calculate_excel_order("description", False, {})
        assert order2 == 26  # 特定字段从26开始

        # 测试自定义顺序
        field_def_with_order = {"excel_order": 10}
        order3 = field_metadata_builder._calculate_excel_order("test", True, field_def_with_order)
        assert order3 == 10

    def test_validate_field_metadata_record(self, field_metadata_builder):
        """测试验证字段元数据记录"""
        # 创建有效记录
        valid_record = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="test_field",
            field_type=FieldTypeEnum.STRING,
            is_required=True,
            is_fixed_field=True,
            display_name="测试字段",
            description="测试描述",
            excel_column_order=1,
        )

        is_valid, errors = field_metadata_builder.validate_field_metadata_record(valid_record)
        assert is_valid == True
        assert errors == []

        # 创建无效记录
        invalid_record = RuleFieldMetadata(
            rule_key="",  # 空的rule_key
            field_name="",  # 空的field_name
            field_type=FieldTypeEnum.STRING,
            is_required=True,
            is_fixed_field=True,
            display_name="",  # 空的display_name
            description="测试描述",
            excel_column_order=0,  # 无效的Excel顺序
        )

        is_valid, errors = field_metadata_builder.validate_field_metadata_record(invalid_record)
        assert is_valid == False
        assert len(errors) == 4  # 应该有4个错误


@pytest.mark.unit
@pytest.mark.field_metadata
class TestValidationEngine:
    """验证引擎测试类"""

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "version": "3.1.0",
            "rule_type_mappings": {
                "test_rule": {"name": "测试规则", "required_fields": ["rule_name"], "optional_fields": ["description"]}
            },
            "field_definitions": {
                "common_fields": {"rule_name": {"chinese_name": "规则名称", "data_type": "string"}},
                "specific_fields": {"description": {"chinese_name": "描述", "data_type": "text"}},
            },
        }

    @pytest.fixture
    def mock_field_mapping_manager(self, sample_config):
        """模拟字段映射管理器"""
        manager = Mock(spec=FieldMappingManager)
        manager.config = sample_config
        return manager

    @pytest.fixture
    def validation_engine(self, mock_field_mapping_manager):
        """验证引擎实例"""
        return ValidationEngine(mock_field_mapping_manager)

    def test_validate_config_completeness_valid(self, validation_engine):
        """测试验证配置完整性 - 有效配置"""
        is_valid, errors = validation_engine.validate_config_completeness()
        assert is_valid == True
        assert errors == []

    def test_validate_config_completeness_missing_keys(self, mock_field_mapping_manager):
        """测试验证配置完整性 - 缺少必要键"""
        mock_field_mapping_manager.config = {"version": "3.1.0"}  # 缺少其他键
        validation_engine = ValidationEngine(mock_field_mapping_manager)

        is_valid, errors = validation_engine.validate_config_completeness()
        assert is_valid == False
        assert len(errors) >= 2  # 至少缺少2个键

    def test_validate_field_consistency_valid(self, validation_engine):
        """测试验证字段一致性 - 有效配置"""
        is_valid, errors = validation_engine.validate_field_consistency()
        assert is_valid == True
        assert errors == []

    def test_validate_field_consistency_undefined_field(self, mock_field_mapping_manager):
        """测试验证字段一致性 - 未定义字段"""
        config = mock_field_mapping_manager.config.copy()
        config["rule_type_mappings"]["test_rule"]["required_fields"] = ["undefined_field"]
        mock_field_mapping_manager.config = config

        validation_engine = ValidationEngine(mock_field_mapping_manager)
        is_valid, errors = validation_engine.validate_field_consistency()
        assert is_valid == False
        assert any("undefined_field" in error for error in errors)

    def test_generate_validation_report(self, validation_engine):
        """测试生成验证报告"""
        report = validation_engine.generate_validation_report()

        assert "timestamp" in report
        assert "overall_status" in report
        assert "total_errors" in report
        assert "total_warnings" in report
        assert "validation_results" in report
        assert "config_completeness" in report["validation_results"]
        assert "field_consistency" in report["validation_results"]


@pytest.mark.unit
@pytest.mark.field_metadata
class TestRuleFieldMetadataInitializer:
    """字段元数据初始化器测试类"""

    @pytest.fixture
    def in_memory_db(self):
        """内存数据库"""
        engine = create_engine("sqlite:///:memory:", poolclass=StaticPool, connect_args={"check_same_thread": False})
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)  # noqa: N806
        return SessionLocal

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "version": "3.1.0",
            "rule_type_mappings": {
                "test_rule_1": {
                    "name": "测试规则1",
                    "required_fields": ["rule_name", "level1"],
                    "optional_fields": ["description"],
                },
                "test_rule_2": {"name": "测试规则2", "required_fields": ["rule_name"], "optional_fields": ["level2"]},
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {"chinese_name": "规则名称", "data_type": "string", "description": "规则的显示名称"},
                    "level1": {"chinese_name": "一级错误类型", "data_type": "string", "description": "一级错误类型"},
                    "level2": {"chinese_name": "二级错误类型", "data_type": "string", "description": "二级错误类型"},
                },
                "specific_fields": {
                    "description": {"chinese_name": "描述", "data_type": "text", "description": "详细描述"}
                },
            },
        }

    @pytest.fixture
    def mock_field_mapping_manager(self, sample_config):
        """模拟字段映射管理器"""
        manager = Mock(spec=FieldMappingManager)
        manager.config = sample_config
        manager.get_field_definition.side_effect = lambda field_name: sample_config["field_definitions"][
            "common_fields"
        ].get(field_name, sample_config["field_definitions"]["specific_fields"].get(field_name))
        return manager

    @pytest.fixture
    def initializer(self, in_memory_db, mock_field_mapping_manager):
        """字段元数据初始化器实例"""
        return RuleFieldMetadataInitializer(in_memory_db, mock_field_mapping_manager)

    def test_initialization(self, initializer):
        """测试初始化器创建"""
        assert initializer.session_factory is not None
        assert initializer.field_mapping_manager is not None
        assert initializer.field_metadata_builder is not None
        assert isinstance(initializer.stats, InitializationStats)

    def test_get_rule_type_mappings(self, initializer):
        """测试获取规则类型映射"""
        mappings = initializer._get_rule_type_mappings()
        assert len(mappings) == 2
        assert "test_rule_1" in mappings
        assert "test_rule_2" in mappings

    def test_get_rule_type_mappings_no_config(self, in_memory_db):
        """测试获取规则类型映射 - 无配置"""
        mock_manager = Mock(spec=FieldMappingManager)
        mock_manager.config = None
        initializer = RuleFieldMetadataInitializer(in_memory_db, mock_manager)

        with pytest.raises(RuleFieldMetadataInitializerError, match="字段映射配置未加载"):
            initializer._get_rule_type_mappings()

    def test_initialize_all_metadata_full_mode(self, initializer):
        """测试完整初始化 - 完全模式"""
        result = initializer.initialize_all_metadata(mode="full")

        assert result["total_rule_types"] == 2
        assert result["created_templates"] >= 0
        assert result["created_field_metadata"] >= 0
        assert "start_time" in result
        assert "end_time" in result

    def test_initialize_all_metadata_incremental_mode(self, initializer):
        """测试完整初始化 - 增量模式"""
        # 先运行一次完整初始化
        initializer.initialize_all_metadata(mode="full")

        # 再运行增量初始化
        result = initializer.initialize_all_metadata(mode="incremental")

        assert result["total_rule_types"] == 2
        assert "start_time" in result
        assert "end_time" in result

    def test_initialize_all_metadata_invalid_mode(self, initializer):
        """测试完整初始化 - 无效模式"""
        with pytest.raises(RuleFieldMetadataInitializerError, match="不支持的初始化模式"):
            initializer.initialize_all_metadata(mode="invalid")

    @patch("services.rule_field_metadata_initializer.logger")
    def test_validate_rule_templates_exist(self, mock_logger, initializer):
        """测试验证规则模板存在"""
        with initializer.session_factory() as session:
            rule_type_mappings = initializer._get_rule_type_mappings()

            # 如果没有规则模板，应该抛出异常
            if not rule_type_mappings:
                return  # 跳过测试，因为没有规则类型映射

            # 验证规则模板存在性检查
            try:
                initializer._validate_rule_templates_exist(session, rule_type_mappings)
                # 如果没有抛出异常，说明所有规则模板都存在
                assert True
            except Exception as e:
                # 如果抛出异常，说明缺少规则模板
                assert "缺少规则模板记录" in str(e)

    def test_create_field_metadata(self, initializer):
        """测试创建字段元数据"""
        with initializer.session_factory() as session:
            # 先创建模板
            rule_type_mappings = initializer._get_rule_type_mappings()
            initializer._create_rule_templates(session, rule_type_mappings, "full")

            # 创建字段元数据
            initializer._create_field_metadata(session, rule_type_mappings, "full")

            # 验证字段元数据是否创建
            metadata = session.query(RuleFieldMetadata).all()
            assert len(metadata) >= 0

            # 验证统计信息
            assert initializer.stats.created_field_metadata >= 0

    def test_build_field_metadata_record(self, initializer):
        """测试构建字段元数据记录"""
        record = initializer._build_field_metadata_record("test_rule", "rule_name", True)

        if record:  # 如果成功构建
            assert isinstance(record, RuleFieldMetadata)
            assert record.rule_key == "test_rule"
            assert record.field_name == "rule_name"
            assert record.is_required == True

    def test_get_initialization_report(self, initializer):
        """测试获取初始化报告"""
        # 运行初始化
        initializer.initialize_all_metadata(mode="full")

        # 获取报告
        report = initializer.get_initialization_report()

        assert isinstance(report, str)
        assert "字段元数据初始化报告" in report
        assert "执行时长" in report
        assert "规则类型总数" in report

    @patch("services.rule_field_metadata_initializer.logger")
    def test_error_handling(self, mock_logger, in_memory_db):
        """测试错误处理"""
        # 创建一个会导致错误的配置
        mock_manager = Mock(spec=FieldMappingManager)
        mock_manager.config = {"invalid": "config"}
        mock_manager.get_field_definition.side_effect = Exception("Test error")

        initializer = RuleFieldMetadataInitializer(in_memory_db, mock_manager)

        with pytest.raises(RuleFieldMetadataInitializerError):
            initializer.initialize_all_metadata(mode="full")


@pytest.mark.integration
@pytest.mark.field_metadata
class TestRuleFieldMetadataInitializerIntegration:
    """字段元数据初始化器集成测试"""

    @pytest.fixture
    def temp_config_file(self):
        """临时配置文件"""
        config_data = {
            "version": "3.1.0",
            "rule_type_mappings": {
                "integration_test_rule": {
                    "name": "集成测试规则",
                    "required_fields": ["rule_name"],
                    "optional_fields": ["description"],
                }
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {"chinese_name": "规则名称", "data_type": "string", "description": "规则的显示名称"}
                },
                "specific_fields": {
                    "description": {"chinese_name": "描述", "data_type": "text", "description": "详细描述"}
                },
            },
        }

        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        yield temp_file

        # 清理
        Path(temp_file).unlink(missing_ok=True)

    @pytest.fixture
    def in_memory_db(self):
        """内存数据库"""
        engine = create_engine("sqlite:///:memory:", poolclass=StaticPool, connect_args={"check_same_thread": False})
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)  # noqa: N806
        return SessionLocal

    def test_full_initialization_workflow(self, temp_config_file, in_memory_db):
        """测试完整的初始化工作流程"""
        # 创建真实的字段映射管理器
        field_mapping_manager = FieldMappingManager(temp_config_file)

        # 创建初始化器
        initializer = RuleFieldMetadataInitializer(in_memory_db, field_mapping_manager)

        # 执行初始化
        result = initializer.initialize_all_metadata(mode="full")

        # 验证结果
        assert result["total_rule_types"] == 1
        assert result["total_errors"] == 0

        # 验证数据库中的数据
        with in_memory_db() as session:
            templates = session.query(RuleTemplate).all()
            metadata = session.query(RuleFieldMetadata).all()

            assert len(templates) >= 1
            assert len(metadata) >= 1

            # 验证模板数据
            template = templates[0]
            assert template.rule_key == "integration_test_rule"
            assert template.name == "集成测试规则"

            # 验证字段元数据
            rule_name_metadata = (
                session.query(RuleFieldMetadata).filter(RuleFieldMetadata.field_name == "rule_name").first()
            )
            assert rule_name_metadata is not None
            assert rule_name_metadata.is_required == True
            assert rule_name_metadata.display_name == "规则名称"
