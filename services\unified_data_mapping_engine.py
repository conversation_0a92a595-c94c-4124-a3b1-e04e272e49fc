"""
统一数据映射引擎
基于FieldMappingManager的数据转换引擎，提供统一的字段映射和数据验证功能
支持高性能批量处理、缓存机制和master-slave架构数据同步
"""

import gzip
import hashlib
import json
import logging
import pickle
import time
from typing import Any

from core.constants.error_codes import ErrorCodes
from tools.field_mapping_manager import FieldMappingManager

logger = logging.getLogger(__name__)


class DataValidationError(Exception):
    """数据验证相关异常"""

    def __init__(self, message: str, field_name: str = None, validation_errors: list = None, suggestions: list = None):
        super().__init__(message)
        self.field_name = field_name
        self.validation_errors = validation_errors or []
        self.suggestions = suggestions or []


class DataMappingError(Exception):
    """数据映射相关异常"""

    def __init__(self, message: str, operation: str = None, data_context: dict = None):
        super().__init__(message)
        self.operation = operation
        self.data_context = data_context or {}


class ValidationResult:
    """验证结果封装类"""

    def __init__(
        self, valid: bool = True, errors: list[str] = None, warnings: list[str] = None, suggestions: list[str] = None
    ):
        self.valid = valid
        self.errors = errors or []
        self.warnings = warnings or []
        self.suggestions = suggestions or []
        self.timestamp = time.time()

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "valid": self.valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "suggestions": self.suggestions,
            "timestamp": self.timestamp,
        }

    def to_api_response_data(self) -> dict[str, Any]:
        """
        转换为符合项目API响应格式的数据结构

        Returns:
            dict: 符合ApiResponse.data字段格式的数据
        """
        return {
            "validation_result": {
                "valid": self.valid,
                "errors": self.errors,
                "warnings": self.warnings,
                "suggestions": self.suggestions,
                "error_count": len(self.errors),
                "warning_count": len(self.warnings),
                "suggestion_count": len(self.suggestions),
                "timestamp": self.timestamp,
            }
        }

    def to_api_response_format(self) -> dict[str, Any]:
        """
        转换为完整的API响应格式

        Returns:
            dict: 符合项目ApiResponse格式的完整响应
        """
        if self.valid:
            return {
                "code": ErrorCodes.SUCCESS,
                "success": True,
                "message": "数据验证通过",
                "data": self.to_api_response_data(),
                "timestamp": self.timestamp,
            }
        else:
            # 构建错误消息
            error_summary = f"发现 {len(self.errors)} 个错误"
            if self.warnings:
                error_summary += f"，{len(self.warnings)} 个警告"

            return {
                "code": ErrorCodes.RULE_DETAIL_VALIDATION_FAILED,  # 使用统一的错误码管理
                "success": False,
                "message": f"数据验证失败：{error_summary}",
                "data": self.to_api_response_data(),
                "timestamp": self.timestamp,
            }

    def __getitem__(self, key: str):
        """支持字典式访问（向后兼容）"""
        if key == "valid":
            return self.valid
        elif key == "errors":
            return self.errors
        elif key == "warnings":
            return self.warnings
        elif key == "suggestions":
            return self.suggestions
        elif key == "timestamp":
            return self.timestamp
        else:
            raise KeyError(f"Key '{key}' not found in ValidationResult")

    def __contains__(self, key: str) -> bool:
        """支持 'in' 操作符"""
        return key in ["valid", "errors", "warnings", "suggestions", "timestamp"]

    def get(self, key: str, default=None):
        """支持 get 方法"""
        try:
            return self[key]
        except KeyError:
            return default


class UnifiedDataMappingEngine:
    """
    统一数据映射引擎

    基于FieldMappingManager提供统一的数据转换、验证和字段分离功能。
    支持高性能批量处理、缓存机制和master-slave架构数据同步。
    确保前后端数据处理的一致性和标准化。
    """

    def __init__(self, config_path: str = "data/field_mapping.json", enable_cache: bool = True, cache_size: int = 1000):
        """
        初始化统一数据映射引擎

        Args:
            config_path: 字段映射配置文件路径
            enable_cache: 是否启用缓存机制
            cache_size: 缓存大小
        """
        self.field_manager = FieldMappingManager(config_path)
        self.enable_cache = enable_cache
        self.cache_size = cache_size

        # 固定字段名称集合（用于字段分离）
        self._fixed_field_names = self._build_fixed_field_names()

        # 性能缓存
        self._validation_cache = {} if enable_cache else None
        self._conversion_cache = {} if enable_cache else None
        self._field_mapping_cache = {} if enable_cache else None

        # 统计信息
        self._stats = {
            "total_validations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "batch_operations": 0,
            "total_processing_time": 0.0,
        }

        logger.info(f"统一数据映射引擎初始化完成，缓存: {'启用' if enable_cache else '禁用'}")

    def get_stats(self) -> dict[str, Any]:
        """获取引擎统计信息"""
        cache_hit_rate = 0.0
        if self._stats["total_validations"] > 0:
            cache_hit_rate = self._stats["cache_hits"] / self._stats["total_validations"] * 100

        return {
            **self._stats,
            "cache_hit_rate": f"{cache_hit_rate:.2f}%",
            "avg_processing_time": self._stats["total_processing_time"] / max(1, self._stats["total_validations"]),
        }

    def clear_cache(self) -> None:
        """清空所有缓存"""
        if self.enable_cache:
            self._validation_cache.clear()
            self._conversion_cache.clear()
            self._field_mapping_cache.clear()
            logger.info("缓存已清空")

    def _build_fixed_field_names(self) -> set:
        """构建固定字段名称集合"""
        # 获取所有通用字段作为固定字段
        common_fields = self.field_manager.get_common_fields()
        fixed_fields = set(common_fields.keys())

        # 添加一些系统字段（注意：不包含id，因为id是自增字段，不应在创建时设置）
        system_fields = {"created_at", "updated_at", "status", "rule_key"}
        fixed_fields.update(system_fields)

        logger.debug(f"固定字段集合构建完成，共 {len(fixed_fields)} 个字段")
        return fixed_fields

    def normalize_field_names(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        标准化字段名称

        Args:
            data: 原始数据字典

        Returns:
            标准化后的数据字典
        """
        if not data:
            return {}

        start_time = time.time()
        normalized_data = {}

        for field_name, value in data.items():
            # 使用缓存获取标准字段名
            standard_name = self._get_cached_standard_field_name(field_name)
            normalized_data[standard_name] = value

        processing_time = time.time() - start_time
        self._stats["total_processing_time"] += processing_time

        logger.debug(f"字段名称标准化完成，处理 {len(data)} 个字段，耗时 {processing_time:.4f}s")
        return normalized_data

    def batch_normalize_field_names(self, data_list: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        批量标准化字段名称

        Args:
            data_list: 原始数据字典列表

        Returns:
            标准化后的数据字典列表
        """
        if not data_list:
            return []

        start_time = time.time()
        self._stats["batch_operations"] += 1

        # 预构建字段映射缓存
        all_field_names = set()
        for data in data_list:
            all_field_names.update(data.keys())

        # 批量获取标准字段名映射
        field_mapping = {}
        for field_name in all_field_names:
            field_mapping[field_name] = self._get_cached_standard_field_name(field_name)

        # 批量处理数据
        normalized_list = []
        for data in data_list:
            normalized_data = {}
            for field_name, value in data.items():
                standard_name = field_mapping[field_name]
                normalized_data[standard_name] = value
            normalized_list.append(normalized_data)

        processing_time = time.time() - start_time
        self._stats["total_processing_time"] += processing_time

        logger.info(f"批量字段名称标准化完成，处理 {len(data_list)} 条记录，耗时 {processing_time:.4f}s")
        return normalized_list

    def _get_cached_standard_field_name(self, field_name: str) -> str:
        """获取缓存的标准字段名"""
        if self.enable_cache and field_name in self._field_mapping_cache:
            self._stats["cache_hits"] += 1
            return self._field_mapping_cache[field_name]

        self._stats["cache_misses"] += 1
        standard_name = self.field_manager.get_standard_field_name(field_name)

        if self.enable_cache:
            # 限制缓存大小
            if len(self._field_mapping_cache) >= self.cache_size:
                # 简单的LRU：删除最旧的一半
                items = list(self._field_mapping_cache.items())
                self._field_mapping_cache = dict(items[len(items) // 2 :])

            self._field_mapping_cache[field_name] = standard_name

        return standard_name

    def convert_to_chinese_fields(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        转换为中文字段名

        Args:
            data: 原始数据字典

        Returns:
            中文字段名的数据字典
        """
        if not data:
            return {}

        chinese_data = {}

        for field_name, value in data.items():
            chinese_name = self.field_manager.get_chinese_name(field_name)
            chinese_data[chinese_name] = value

        logger.debug(f"中文字段转换完成，处理 {len(data)} 个字段")
        return chinese_data

    def convert_to_database_fields(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        转换为数据库字段名

        Args:
            data: 原始数据字典

        Returns:
            数据库字段名的数据字典
        """
        if not data:
            return {}

        db_data = {}

        for field_name, value in data.items():
            db_column = self.field_manager.get_database_column(field_name)
            db_data[db_column] = value

        logger.debug(f"数据库字段转换完成，处理 {len(data)} 个字段")
        return db_data

    def convert_to_api_fields(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        转换为API字段名

        Args:
            data: 原始数据字典

        Returns:
            API字段名的数据字典
        """
        if not data:
            return {}

        api_data = {}

        for field_name, value in data.items():
            api_field = self.field_manager.get_api_field(field_name)
            api_data[api_field] = value

        logger.debug(f"API字段转换完成，处理 {len(data)} 个字段")
        return api_data

    def validate_data(self, data: dict[str, Any], rule_type: str = None) -> ValidationResult:
        """
        验证数据（增强版）

        Args:
            data: 待验证的数据字典
            rule_type: 规则类型（可选）

        Returns:
            ValidationResult对象
        """
        if not data:
            return ValidationResult(valid=True)

        start_time = time.time()
        self._stats["total_validations"] += 1

        # 检查缓存
        cache_key = self._generate_validation_cache_key(data, rule_type)
        if self.enable_cache and cache_key in self._validation_cache:
            self._stats["cache_hits"] += 1
            cached_result = self._validation_cache[cache_key]
            logger.debug("使用缓存的验证结果")
            return cached_result

        self._stats["cache_misses"] += 1

        errors = []
        warnings = []
        suggestions = []

        try:
            # 如果指定了规则类型，检查必填字段
            if rule_type:
                required_errors, required_suggestions = self.validate_required_fields_enhanced(data, rule_type)
                errors.extend(required_errors)
                suggestions.extend(required_suggestions)

            # 验证每个字段的格式
            for field_name, value in data.items():
                field_errors, field_warnings, field_suggestions = self._validate_field_value_enhanced(field_name, value)
                errors.extend(field_errors)
                warnings.extend(field_warnings)
                suggestions.extend(field_suggestions)

            # 创建验证结果
            result = ValidationResult(valid=len(errors) == 0, errors=errors, warnings=warnings, suggestions=suggestions)

            # 缓存结果
            if self.enable_cache:
                self._cache_validation_result(cache_key, result)

            processing_time = time.time() - start_time
            self._stats["total_processing_time"] += processing_time

            logger.debug(
                f"数据验证完成，发现 {len(errors)} 个错误，{len(warnings)} 个警告，"
                f"{len(suggestions)} 个建议，耗时 {processing_time:.4f}s"
            )
            return result

        except Exception as e:
            logger.error(f"数据验证过程中发生异常: {e}")
            raise DataValidationError(f"数据验证失败: {str(e)}", validation_errors=errors) from None

    def batch_validate_data(self, data_list: list[dict[str, Any]], rule_type: str = None) -> list[ValidationResult]:
        """
        批量验证数据

        Args:
            data_list: 待验证的数据字典列表
            rule_type: 规则类型（可选）

        Returns:
            ValidationResult对象列表
        """
        if not data_list:
            return []

        start_time = time.time()
        self._stats["batch_operations"] += 1

        results = []
        for data in data_list:
            result = self.validate_data(data, rule_type)
            results.append(result)

        processing_time = time.time() - start_time
        logger.info(f"批量数据验证完成，处理 {len(data_list)} 条记录，耗时 {processing_time:.4f}s")

        return results

    def _generate_validation_cache_key(self, data: dict[str, Any], rule_type: str = None) -> str:
        """生成验证缓存键"""
        data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        rule_type_str = rule_type or ""
        combined = f"{data_str}|{rule_type_str}"
        return hashlib.md5(combined.encode("utf-8")).hexdigest()

    def _cache_validation_result(self, cache_key: str, result: ValidationResult) -> None:
        """缓存验证结果"""
        if len(self._validation_cache) >= self.cache_size:
            # 简单的LRU：删除最旧的一半
            items = list(self._validation_cache.items())
            self._validation_cache = dict(items[len(items) // 2 :])

        self._validation_cache[cache_key] = result

    def validate_required_fields_enhanced(self, data: dict[str, Any], rule_type: str) -> tuple[list[str], list[str]]:
        """
        验证必填字段（增强版）

        Args:
            data: 数据字典
            rule_type: 规则类型

        Returns:
            Tuple[错误信息列表, 建议列表]
        """
        errors = []
        suggestions = []

        try:
            # 获取规则类型的字段要求
            field_requirements = self.field_manager.get_rule_type_fields(rule_type)
            required_fields = field_requirements.get("required", [])

            # 检查必填字段
            for required_field in required_fields:
                if required_field not in data or not data[required_field]:
                    chinese_name = self.field_manager.get_chinese_name(required_field)
                    errors.append(f"缺少必填字段: {chinese_name} ({required_field})")

                    # 提供修复建议
                    field_def = self.field_manager.get_field_definition(required_field)
                    if field_def:
                        default_value = field_def.get("default_value")
                        if default_value:
                            suggestions.append(f"建议为 {chinese_name} 设置默认值: {default_value}")
                        else:
                            suggestions.append(f"请为 {chinese_name} 提供有效值")

            return errors, suggestions

        except Exception as e:
            logger.error(f"验证必填字段时发生异常: {e}")
            return [f"必填字段验证失败: {str(e)}"], []

    def validate_required_fields(self, data: dict[str, Any], rule_type: str) -> list[str]:
        """
        验证必填字段（兼容性方法）

        Args:
            data: 数据字典
            rule_type: 规则类型

        Returns:
            错误信息列表
        """
        errors, _ = self.validate_required_fields_enhanced(data, rule_type)
        return errors

    def _validate_field_value_enhanced(self, field_name: str, value: Any) -> tuple[list[str], list[str], list[str]]:
        """
        验证单个字段值（增强版）

        Args:
            field_name: 字段名称
            value: 字段值

        Returns:
            Tuple[错误列表, 警告列表, 建议列表]
        """
        errors = []
        warnings = []
        suggestions = []

        try:
            # 获取字段验证规则
            validation_rules = self.field_manager.get_validation_rules(field_name)
            chinese_name = self.field_manager.get_chinese_name(field_name)
            # field_def = self.field_manager.get_field_definition(field_name)

            if not validation_rules:
                return errors, warnings, suggestions

            for rule in validation_rules:
                try:
                    if rule == "required" and not value:
                        errors.append(f"{chinese_name}不能为空")
                        suggestions.append(f"请为{chinese_name}提供有效值")

                    elif rule.startswith("max_length:"):
                        max_len = int(rule.split(":")[1])
                        if isinstance(value, str):
                            if len(value) > max_len:
                                errors.append(f"{chinese_name}长度不能超过{max_len}个字符，当前长度: {len(value)}")
                                suggestions.append(f"请将{chinese_name}截断到{max_len}个字符以内")
                            elif len(value) > max_len * 0.8:
                                warnings.append(f"{chinese_name}长度接近限制({len(value)}/{max_len})")

                    elif rule.startswith("min_length:"):
                        min_len = int(rule.split(":")[1])
                        if isinstance(value, str) and len(value) < min_len:
                            errors.append(f"{chinese_name}长度不能少于{min_len}个字符，当前长度: {len(value)}")
                            suggestions.append(f"请为{chinese_name}提供至少{min_len}个字符")

                    elif rule == "integer":
                        if not isinstance(value, int):
                            try:
                                int(value)
                                warnings.append(f"{chinese_name}应为整数类型，当前为字符串")
                                suggestions.append(f"建议将{chinese_name}转换为整数类型")
                            except (ValueError, TypeError):
                                errors.append(f"{chinese_name}必须为整数，当前值: {value}")
                                suggestions.append(f"请为{chinese_name}提供有效的整数值")

                    elif rule == "array":
                        if not isinstance(value, list | tuple):
                            if isinstance(value, str) and "," in value:
                                warnings.append(f"{chinese_name}应为数组格式，当前为逗号分隔字符串")
                                suggestions.append(f"建议将{chinese_name}转换为数组格式")
                            else:
                                errors.append(f"{chinese_name}必须为数组格式，当前类型: {type(value).__name__}")
                                suggestions.append(f"请为{chinese_name}提供数组格式的值")

                    elif rule.startswith("min:"):
                        min_val = float(rule.split(":")[1])
                        if isinstance(value, int | float) and value < min_val:
                            errors.append(f"{chinese_name}不能小于{min_val}，当前值: {value}")
                            suggestions.append(f"请为{chinese_name}设置大于等于{min_val}的值")

                    elif rule.startswith("max:"):
                        max_val = float(rule.split(":")[1])
                        if isinstance(value, int | float) and value > max_val:
                            errors.append(f"{chinese_name}不能大于{max_val}，当前值: {value}")
                            suggestions.append(f"请为{chinese_name}设置小于等于{max_val}的值")

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析验证规则 '{rule}' 失败: {e}")
                    warnings.append(f"{chinese_name}的验证规则配置有误")

            return errors, warnings, suggestions

        except Exception as e:
            logger.error(f"验证字段 '{field_name}' 时发生异常: {e}")
            return [f"字段验证失败: {str(e)}"], [], []

    def _validate_field_value(self, field_name: str, value: Any) -> list[str]:
        """
        验证单个字段值（兼容性方法）

        Args:
            field_name: 字段名称
            value: 字段值

        Returns:
            错误信息列表
        """
        errors, _, _ = self._validate_field_value_enhanced(field_name, value)
        return errors

    def separate_fields(self, rule_data: dict[str, Any]) -> tuple[dict[str, Any], dict[str, Any]]:
        """
        分离固定字段和扩展字段

        Args:
            rule_data: 规则数据字典

        Returns:
            (固定字段字典, 扩展字段字典)
        """
        if not rule_data:
            return {}, {}

        fixed_fields = {}
        extended_fields = {}

        for key, value in rule_data.items():
            # 标准化字段名
            standard_key = self.field_manager.get_standard_field_name(key)

            if standard_key in self._fixed_field_names:
                # 处理数组字段的特殊逻辑
                if self._is_array_field(standard_key):
                    fixed_fields[standard_key] = self._process_array_field(value)
                else:
                    fixed_fields[standard_key] = value
            else:
                extended_fields[standard_key] = value

        logger.debug(f"字段分离完成，固定字段: {len(fixed_fields)}，扩展字段: {len(extended_fields)}")
        return fixed_fields, extended_fields

    def merge_fields(self, fixed_fields: dict[str, Any], extended_fields: dict[str, Any]) -> dict[str, Any]:
        """
        合并固定字段和扩展字段

        Args:
            fixed_fields: 固定字段字典
            extended_fields: 扩展字段字典

        Returns:
            合并后的数据字典
        """
        merged_data = {}

        # 添加固定字段
        if fixed_fields:
            merged_data.update(fixed_fields)

        # 添加扩展字段
        if extended_fields:
            # 如果有扩展字段，可以选择直接合并或放入extended_fields键中
            merged_data.update(extended_fields)

        logger.debug(f"字段合并完成，总字段数: {len(merged_data)}")
        return merged_data

    def _is_array_field(self, field_name: str) -> bool:
        """
        检查字段是否为数组类型

        Args:
            field_name: 字段名称

        Returns:
            是否为数组字段
        """
        field_def = self.field_manager.get_field_definition(field_name)
        if field_def:
            return field_def.get("data_type") == "array"

        # 根据字段名称判断（备用逻辑）
        array_field_patterns = [
            "yb_code",
            "diag_whole_code",
            "diag_code_prefix",
            "fee_whole_code",
            "fee_code_prefix",
            "drug_code_prefix",
        ]
        return field_name in array_field_patterns

    def _process_array_field(self, value: Any) -> str:
        """
        处理数组字段值

        Args:
            value: 字段值

        Returns:
            处理后的字符串值（逗号分隔）
        """
        if value is None:
            return ""

        if isinstance(value, list):
            # 列表转换为逗号分隔的字符串
            return ",".join(str(item) for item in value if item)
        elif isinstance(value, str):
            # 字符串直接返回
            return value
        else:
            # 其他类型转换为字符串
            return str(value)

    def convert_to_structured_format(self, data: dict[str, Any], rule_key: str = None) -> dict[str, Any]:
        """
        转换为结构化格式（用于数据库存储）

        Args:
            data: 原始数据
            rule_key: 规则键（可选）

        Returns:
            结构化格式的数据
        """
        if not data:
            return {}

        # 标准化字段名称
        normalized_data = self.normalize_field_names(data)

        # 分离固定字段和扩展字段
        fixed_fields, extended_fields = self.separate_fields(normalized_data)

        # 构建结构化数据
        structured_data = fixed_fields.copy()

        # 如果有扩展字段，添加到extended_fields中
        if extended_fields:
            structured_data["extended_fields"] = json.dumps(extended_fields, ensure_ascii=False)

        # 添加规则键
        if rule_key:
            structured_data["rule_key"] = rule_key

        logger.debug(f"结构化格式转换完成，固定字段: {len(fixed_fields)}，扩展字段: {len(extended_fields)}")
        return structured_data

    def convert_from_structured_format(self, structured_data: dict[str, Any]) -> dict[str, Any]:
        """
        从结构化格式转换为普通格式

        Args:
            structured_data: 结构化数据

        Returns:
            普通格式的数据
        """
        if not structured_data:
            return {}

        # 复制固定字段
        data = structured_data.copy()

        # 处理扩展字段
        extended_fields_json = data.pop("extended_fields", None)
        if extended_fields_json:
            try:
                extended_fields = json.loads(extended_fields_json)
                data.update(extended_fields)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析扩展字段失败: {e}")

        logger.debug(f"结构化格式解析完成，总字段数: {len(data)}")
        return data

    def get_engine_info(self) -> dict[str, Any]:
        """
        获取引擎信息

        Returns:
            引擎信息字典
        """
        field_manager_info = self.field_manager.get_config_info()

        return {
            "engine_name": "UnifiedDataMappingEngine",
            "version": "2.0.0",
            "fixed_fields_count": len(self._fixed_field_names),
            "field_manager_info": field_manager_info,
            "cache_enabled": self.enable_cache,
            "cache_size": self.cache_size,
            "stats": self.get_stats(),
            "supported_operations": [
                "normalize_field_names",
                "batch_normalize_field_names",
                "convert_to_chinese_fields",
                "convert_to_database_fields",
                "convert_to_api_fields",
                "validate_data",
                "batch_validate_data",
                "separate_fields",
                "merge_fields",
                "convert_to_structured_format",
                "convert_from_structured_format",
                "serialize_for_slave_node",
                "deserialize_from_master_node",
                "batch_serialize_for_slave_node",
                "batch_deserialize_from_master_node",
            ],
            "features": [
                "高性能缓存机制",
                "批量数据处理",
                "增强错误处理",
                "Master-Slave架构支持",
                "数据压缩传输",
                "详细统计信息和监控",
            ],
        }

    # Master-Slave架构支持方法

    def serialize_for_slave_node(self, data: dict[str, Any], compression: bool = True) -> bytes:
        """
        序列化数据用于从节点同步

        Args:
            data: 要序列化的数据
            compression: 是否启用压缩

        Returns:
            序列化后的字节数据
        """
        try:
            # 标准化数据
            normalized_data = self.normalize_field_names(data)

            # 分离固定字段和扩展字段
            fixed_fields, extended_fields = self.separate_fields(normalized_data)

            # 构建序列化数据包
            serialized_data = {
                "version": "1.0",
                "timestamp": time.time(),
                "fixed_fields": fixed_fields,
                "extended_fields": extended_fields,
                "metadata": {
                    "engine_version": "1.0.0",
                    "field_count": len(normalized_data),
                    "compression": compression,
                },
            }

            # 序列化为字节
            data_bytes = pickle.dumps(serialized_data)

            # 可选压缩
            if compression:
                data_bytes = gzip.compress(data_bytes)

            logger.debug(f"数据序列化完成，原始大小: {len(pickle.dumps(data))} bytes，压缩后: {len(data_bytes)} bytes")
            return data_bytes

        except Exception as e:
            logger.error(f"数据序列化失败: {e}")
            raise DataMappingError(f"序列化失败: {str(e)}", operation="serialize_for_slave_node") from None

    def deserialize_from_master_node(self, serialized_data: bytes) -> dict[str, Any]:
        """
        从主节点反序列化数据

        Args:
            serialized_data: 序列化的字节数据

        Returns:
            反序列化后的数据字典
        """
        try:
            # 尝试解压缩
            try:
                decompressed_data = gzip.decompress(serialized_data)
            except gzip.BadGzipFile:
                # 如果不是压缩数据，直接使用原始数据
                decompressed_data = serialized_data

            # 反序列化
            data_package = pickle.loads(decompressed_data)

            # 验证数据包格式
            if not isinstance(data_package, dict) or "version" not in data_package:
                raise ValueError("无效的数据包格式")

            # 合并固定字段和扩展字段
            fixed_fields = data_package.get("fixed_fields", {})
            extended_fields = data_package.get("extended_fields", {})

            merged_data = self.merge_fields(fixed_fields, extended_fields)

            logger.debug(f"数据反序列化完成，字段数量: {len(merged_data)}")
            return merged_data

        except Exception as e:
            logger.error(f"数据反序列化失败: {e}")
            raise DataMappingError(f"反序列化失败: {str(e)}", operation="deserialize_from_master_node") from None

    def batch_serialize_for_slave_node(self, data_list: list[dict[str, Any]], compression: bool = True) -> bytes:
        """
        批量序列化数据用于从节点同步

        Args:
            data_list: 要序列化的数据列表
            compression: 是否启用压缩

        Returns:
            序列化后的字节数据
        """
        try:
            start_time = time.time()
            self._stats["batch_operations"] += 1

            # 批量处理数据
            serialized_list = []
            for data in data_list:
                normalized_data = self.normalize_field_names(data)
                fixed_fields, extended_fields = self.separate_fields(normalized_data)
                serialized_list.append({"fixed_fields": fixed_fields, "extended_fields": extended_fields})

            # 构建批量数据包
            batch_package = {
                "version": "1.0",
                "timestamp": time.time(),
                "batch_size": len(data_list),
                "data": serialized_list,
                "metadata": {
                    "engine_version": "1.0.0",
                    "compression": compression,
                    "processing_time": time.time() - start_time,
                },
            }

            # 序列化为字节
            data_bytes = pickle.dumps(batch_package)

            # 可选压缩
            if compression:
                data_bytes = gzip.compress(data_bytes)

            processing_time = time.time() - start_time
            logger.info(f"批量数据序列化完成，处理 {len(data_list)} 条记录，耗时 {processing_time:.4f}s")
            return data_bytes

        except Exception as e:
            logger.error(f"批量数据序列化失败: {e}")
            raise DataMappingError(f"批量序列化失败: {str(e)}", operation="batch_serialize_for_slave_node") from None

    def batch_deserialize_from_master_node(self, serialized_data: bytes) -> list[dict[str, Any]]:
        """
        从主节点批量反序列化数据

        Args:
            serialized_data: 序列化的字节数据

        Returns:
            反序列化后的数据字典列表
        """
        try:
            # 尝试解压缩
            try:
                decompressed_data = gzip.decompress(serialized_data)
            except gzip.BadGzipFile:
                decompressed_data = serialized_data

            # 反序列化
            batch_package = pickle.loads(decompressed_data)

            # 验证批量数据包格式
            if not isinstance(batch_package, dict) or "batch_size" not in batch_package:
                raise ValueError("无效的批量数据包格式")

            # 批量处理数据
            data_list = []
            for item in batch_package.get("data", []):
                fixed_fields = item.get("fixed_fields", {})
                extended_fields = item.get("extended_fields", {})
                merged_data = self.merge_fields(fixed_fields, extended_fields)
                data_list.append(merged_data)

            logger.info(f"批量数据反序列化完成，处理 {len(data_list)} 条记录")
            return data_list

        except Exception as e:
            logger.error(f"批量数据反序列化失败: {e}")
            raise DataMappingError(f"批量反序列化失败: {str(e)}", operation="batch_deserialize_from_master_node") from e


# ==================== 数组字段转换工具函数 ====================


def convert_db_string_to_list(db_value: str | None) -> list[str]:
    """
    将数据库中的逗号分隔字符串转换为列表

    Args:
        db_value: 数据库中的字符串值，如 "A001,A002,A003"

    Returns:
        转换后的列表，如 ["A001", "A002", "A003"]
        空值返回空列表 []

    Examples:
        >>> convert_db_string_to_list("A001,A002,A003")
        ["A001", "A002", "A003"]
        >>> convert_db_string_to_list("A001, , A003, ")
        ["A001", "A003"]
        >>> convert_db_string_to_list("")
        []
        >>> convert_db_string_to_list(None)
        []
    """
    if not db_value:
        return []
    return [item.strip() for item in db_value.split(",") if item.strip()]


def convert_list_to_db_string(list_value: list[str] | None) -> str:
    """
    将列表转换为数据库存储的逗号分隔字符串

    Args:
        list_value: 列表值，如 ["A001", "A002", "A003"]

    Returns:
        逗号分隔的字符串，如 "A001,A002,A003"
        空列表返回空字符串 ""

    Examples:
        >>> convert_list_to_db_string(["A001", "A002", "A003"])
        "A001,A002,A003"
        >>> convert_list_to_db_string([])
        ""
        >>> convert_list_to_db_string(None)
        ""
    """
    if not list_value:
        return ""
    return ",".join(str(item).strip() for item in list_value if str(item).strip())


def get_array_field_names() -> list[str]:
    """
    获取所有数组字段名称列表

    Returns:
        数组字段名称列表
    """
    return [
        "yb_code",
        "diag_whole_code",
        "diag_code_prefix",
        "fee_whole_code",
        "fee_code_prefix",
        "drug_code_prefix",  # 可能的其他数组字段
    ]


def convert_rule_data_array_fields(rule_data: dict[str, Any], direction: str = "to_list") -> dict[str, Any]:
    """
    批量转换规则数据中的数组字段

    Args:
        rule_data: 规则数据字典
        direction: 转换方向，"to_list" 或 "to_string"

    Returns:
        转换后的规则数据字典
    """
    if not rule_data:
        return {}

    converted_data = rule_data.copy()
    array_fields = get_array_field_names()

    for field_name in array_fields:
        if field_name in converted_data:
            field_value = converted_data[field_name]

            if direction == "to_list" and isinstance(field_value, str):
                converted_data[field_name] = convert_db_string_to_list(field_value)
            elif direction == "to_string" and isinstance(field_value, list):
                converted_data[field_name] = convert_list_to_db_string(field_value)

    return converted_data
