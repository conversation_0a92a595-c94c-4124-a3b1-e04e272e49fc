[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10
    --cov=core
    --cov=api
    --cov=services
    --cov=models
    --cov-report=html:tests/reports/coverage/html
    --cov-report=term-missing
    --cov-report=xml:tests/reports/coverage/coverage.xml
    --junit-xml=tests/reports/junit.xml
    --cov-fail-under=80

markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    e2e: End-to-end tests
    api: API tests
    slow: Slow tests (runtime > 5 seconds)
    fast: Fast tests (runtime < 1 second)
    smoke: Smoke tests
    regression: Regression tests
    critical: Critical functionality tests
    rule_details: Rule details related tests
    migration: Data migration related tests
    config: Configuration related tests
    core: Core component tests
    services: Service layer tests
    models: Model layer tests
    database: Database related tests
    security: Security related tests
    field_mapping: Field mapping related tests
    data_mapping: Data mapping related tests
    requires_database: Tests requiring database
    requires_network: Tests requiring network
    requires_external_service: Tests requiring external services
    
# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# 最小版本要求
minversion = 6.0

# 异步测试支持
asyncio_mode = auto

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试超时
timeout = 300

# 并行测试
# 如果安装了pytest-xdist，可以启用并行测试
# addopts = -n auto
