# ===== 主节点生产环境配置模板 =====
# 复制此文件为 .env.master 并根据实际情况修改配置

# ===== 应用模式配置 =====
# 节点类型: master（主节点）
MODE=master
# 运行环境: PROD（生产环境）
RUN_MODE=PROD

# ===== 服务端口配置 =====
# 主节点后端端口
SERVER_PORT=18001
# 前端服务端口
FRONTEND_PORT=18099
# 服务器监听地址
SERVER_HOST=0.0.0.0

# ===== 数据库配置（生产环境推荐独立参数方式） =====
# 数据库连接参数（优先级高于DATABASE_URL）
DB_HOST=cop.db.com
DB_PORT=3306
DB_USER=rule_service
DB_PASSWORD=rule_service
DB_NAME=rule_service
DB_DRIVER=pymysql

# ===== 安全配置 =====
# 主节点API密钥（生产环境必须使用强密钥）
MASTER_API_SECRET_KEY=your-very-secure-secret-key-for-production-environment

# ===== 性能配置（生产环境优化） =====
# 工作进程数（建议根据CPU核心数调整）
WORKERS=8
# 日志级别（生产环境建议使用 info 或 warning）
LOG_LEVEL=INFO

# ===== 功能开关 =====
# 是否启用规则同步
ENABLE_RULE_SYNC=true
# 是否启用结构化存储（新表结构功能）
ENABLE_STRUCTURED_STORAGE=true
# 是否启用JSON备份（兼容性保障）
ENABLE_JSON_BACKUP=false

# ===== 规则注册服务配置 =====
RULE_REGISTRATION_ENABLED=true
RULE_REGISTRATION_HOST=http://rule.yading.xyz
