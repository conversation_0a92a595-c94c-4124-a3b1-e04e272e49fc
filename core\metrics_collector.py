"""
统一指标收集器
整合系统各个组件的监控指标，提供统一的指标收集和查询接口
"""

import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any

from core.logging.logging_system import log as logger
from core.performance_monitor import performance_monitor


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"  # 计数器（只增不减）
    GAUGE = "gauge"  # 仪表盘（可增可减）
    HISTOGRAM = "histogram"  # 直方图
    SUMMARY = "summary"  # 摘要


class MetricCategory(Enum):
    """指标分类"""
    SYSTEM = "system"  # 系统指标
    BUSINESS = "business"  # 业务指标
    PERFORMANCE = "performance"  # 性能指标
    ERROR = "error"  # 错误指标
    CUSTOM = "custom"  # 自定义指标


@dataclass
class MetricPoint:
    """指标数据点"""
    name: str
    value: int | float
    timestamp: float
    labels: dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE
    category: MetricCategory = MetricCategory.CUSTOM


@dataclass
class MetricSummary:
    """指标摘要"""
    name: str
    category: MetricCategory
    metric_type: MetricType
    current_value: int | float
    min_value: int | float
    max_value: int | float
    avg_value: float
    count: int
    last_updated: float
    labels: dict[str, str] = field(default_factory=dict)


class MetricsCollector:
    """
    统一指标收集器

    负责收集、存储和查询各种系统和业务指标
    """

    def __init__(self, history_size: int = 1000):
        """
        初始化指标收集器

        Args:
            history_size: 每个指标保留的历史数据点数量
        """
        self.history_size = history_size
        self.metrics_data: dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self.metric_metadata: dict[str, dict[str, Any]] = {}
        self.counters: dict[str, float] = defaultdict(float)

        # 预定义的系统指标
        self._register_system_metrics()

        logger.info(f"MetricsCollector initialized with history_size={history_size}")

    def _register_system_metrics(self):
        """注册系统预定义指标"""
        system_metrics = [
            ("system.cpu.usage", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("system.memory.usage", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("system.memory.available", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("system.process.count", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("system.queue.length", MetricType.GAUGE, MetricCategory.SYSTEM),

            # 数据库指标
            ("database.pool.size", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("database.active.connections", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("database.idle.connections", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("database.pool.utilization", MetricType.GAUGE, MetricCategory.SYSTEM),

            # 业务指标
            ("registration.tasks.total", MetricType.COUNTER, MetricCategory.BUSINESS),
            ("registration.tasks.success", MetricType.COUNTER, MetricCategory.BUSINESS),
            ("registration.tasks.failed", MetricType.COUNTER, MetricCategory.BUSINESS),
            ("registration.rules.processed", MetricType.COUNTER, MetricCategory.BUSINESS),
            ("registration.batch.size", MetricType.GAUGE, MetricCategory.BUSINESS),

            # 性能指标
            ("performance.processing.time", MetricType.HISTOGRAM, MetricCategory.PERFORMANCE),
            ("performance.throughput", MetricType.GAUGE, MetricCategory.PERFORMANCE),
            ("performance.memory.usage", MetricType.GAUGE, MetricCategory.PERFORMANCE),

            # 错误指标
            ("errors.total", MetricType.COUNTER, MetricCategory.ERROR),
            ("errors.rate", MetricType.GAUGE, MetricCategory.ERROR),
            ("errors.by.type", MetricType.COUNTER, MetricCategory.ERROR),

            # 降级策略指标
            ("degradation.status", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("degradation.cache.size", MetricType.GAUGE, MetricCategory.SYSTEM),
            ("degradation.circuit.breaker.state", MetricType.GAUGE, MetricCategory.SYSTEM),
        ]

        for name, metric_type, category in system_metrics:
            self.register_metric(name, metric_type, category)

    def register_metric(
        self,
        name: str,
        metric_type: MetricType,
        category: MetricCategory,
        description: str = "",
        labels: dict[str, str] = None,
    ):
        """
        注册指标

        Args:
            name: 指标名称
            metric_type: 指标类型
            category: 指标分类
            description: 指标描述
            labels: 默认标签
        """
        self.metric_metadata[name] = {
            "type": metric_type,
            "category": category,
            "description": description,
            "labels": labels or {},
            "registered_at": time.time()
        }

        logger.debug(f"Metric registered: {name} ({metric_type.value}, {category.value})")

    def record_metric(
        self,
        name: str,
        value: int | float,
        labels: dict[str, str] = None,
        timestamp: float = None,
    ):
        """
        记录指标数据

        Args:
            name: 指标名称
            value: 指标值
            labels: 标签
            timestamp: 时间戳
        """
        if timestamp is None:
            timestamp = time.time()

        # 获取指标元数据
        metadata = self.metric_metadata.get(name, {})
        metric_type = metadata.get("type", MetricType.GAUGE)
        category = metadata.get("category", MetricCategory.CUSTOM)

        # 处理计数器类型
        if metric_type == MetricType.COUNTER:
            self.counters[name] += value
            actual_value = self.counters[name]
        else:
            actual_value = value

        # 创建指标数据点
        point = MetricPoint(
            name=name,
            value=actual_value,
            timestamp=timestamp,
            labels=labels or {},
            metric_type=metric_type,
            category=category
        )

        # 存储数据点
        self.metrics_data[name].append(point)

        logger.trace(f"Metric recorded: {name}={actual_value} at {timestamp}")

    def increment_counter(self, name: str, value: float = 1.0, labels: dict[str, str] = None):
        """
        增加计数器

        Args:
            name: 计数器名称
            value: 增加的值
            labels: 标签
        """
        self.record_metric(name, value, labels)

    def set_gauge(self, name: str, value: int | float, labels: dict[str, str] = None):
        """
        设置仪表盘值

        Args:
            name: 仪表盘名称
            value: 设置的值
            labels: 标签
        """
        self.record_metric(name, value, labels)

    def get_metric_current_value(self, name: str) -> int | float | None:
        """
        获取指标当前值

        Args:
            name: 指标名称

        Returns:
            当前值，如果不存在则返回None
        """
        if name not in self.metrics_data or not self.metrics_data[name]:
            return None

        return self.metrics_data[name][-1].value

    def get_metric_history(self, name: str, limit: int = None) -> list[MetricPoint]:
        """
        获取指标历史数据

        Args:
            name: 指标名称
            limit: 限制返回的数据点数量

        Returns:
            指标历史数据列表
        """
        if name not in self.metrics_data:
            return []

        data = list(self.metrics_data[name])
        if limit:
            data = data[-limit:]

        return data

    def get_metric_summary(self, name: str, window_seconds: int = None) -> MetricSummary | None:
        """
        获取指标摘要统计

        Args:
            name: 指标名称
            window_seconds: 统计时间窗口（秒），None表示所有历史数据

        Returns:
            指标摘要统计
        """
        if name not in self.metrics_data or not self.metrics_data[name]:
            return None

        data = list(self.metrics_data[name])

        # 应用时间窗口过滤
        if window_seconds:
            cutoff_time = time.time() - window_seconds
            data = [point for point in data if point.timestamp >= cutoff_time]

        if not data:
            return None

        # 计算统计信息
        values = [point.value for point in data]
        metadata = self.metric_metadata.get(name, {})

        return MetricSummary(
            name=name,
            category=metadata.get("category", MetricCategory.CUSTOM),
            metric_type=metadata.get("type", MetricType.GAUGE),
            current_value=values[-1],
            min_value=min(values),
            max_value=max(values),
            avg_value=sum(values) / len(values),
            count=len(values),
            last_updated=data[-1].timestamp,
            labels=data[-1].labels
        )

    def get_metrics_by_category(self, category: MetricCategory) -> dict[str, MetricSummary]:
        """
        按分类获取指标摘要

        Args:
            category: 指标分类

        Returns:
            指标摘要字典
        """
        result = {}

        for name, metadata in self.metric_metadata.items():
            if metadata.get("category") == category:
                summary = self.get_metric_summary(name)
                if summary:
                    result[name] = summary

        return result

    def collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 从性能监控器获取当前指标
            current_metrics = performance_monitor.get_current_metrics()
            if current_metrics:
                self.set_gauge("system.cpu.usage", current_metrics.cpu_usage)
                self.set_gauge("system.memory.usage", current_metrics.memory_usage)
                self.set_gauge("system.memory.available", current_metrics.memory_available)
                self.set_gauge("system.process.count", current_metrics.process_count)
                self.set_gauge("system.queue.length", current_metrics.queue_length)

                # 数据库指标
                self.set_gauge("database.pool.size", current_metrics.db_pool_size)
                self.set_gauge("database.active.connections", current_metrics.db_active_connections)
                self.set_gauge("database.idle.connections", current_metrics.db_idle_connections)
                self.set_gauge("database.pool.utilization", current_metrics.db_pool_utilization)

            # 从注册任务性能监控获取指标
            reg_stats = performance_monitor.get_registration_performance_stats()
            if reg_stats:
                self.set_gauge("registration.batch.size", reg_stats.avg_batch_size)
                self.set_gauge("performance.processing.time", reg_stats.avg_processing_time)
                self.set_gauge("performance.throughput", reg_stats.throughput)
                self.set_gauge("performance.memory.usage", reg_stats.avg_memory_usage)

                # 计算成功率
                if reg_stats.total_tasks > 0:
                    success_rate = reg_stats.successful_tasks / reg_stats.total_tasks
                    self.set_gauge("registration.tasks.success.rate", success_rate)

        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

    def get_all_metrics_summary(self) -> dict[str, Any]:
        """
        获取所有指标的摘要信息

        Returns:
            包含所有指标摘要的字典
        """
        summary = {
            "timestamp": time.time(),
            "total_metrics": len(self.metric_metadata),
            "categories": {}
        }

        # 按分类组织指标
        for category in MetricCategory:
            category_metrics = self.get_metrics_by_category(category)
            if category_metrics:
                summary["categories"][category.value] = {
                    "count": len(category_metrics),
                    "metrics": {name: {
                        "current_value": metric.current_value,
                        "min_value": metric.min_value,
                        "max_value": metric.max_value,
                        "avg_value": metric.avg_value,
                        "last_updated": metric.last_updated
                    } for name, metric in category_metrics.items()}
                }

        return summary


# 全局指标收集器实例
metrics_collector = MetricsCollector()
