<template>
  <div class="batch-operation-history">
    <el-card shadow="never" class="history-card">
      <template #header>
        <div class="history-header">
          <div class="header-left">
            <el-icon class="header-icon"><Clock /></el-icon>
            <span class="header-title">操作历史</span>
          </div>
          <div class="header-right">
            <el-button
              size="small"
              :icon="RefreshRight"
              @click="refreshHistory"
              :loading="loading"
            >
              刷新
            </el-button>
            <el-button
              size="small"
              :icon="Delete"
              @click="clearHistory"
              :disabled="historyList.length === 0"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>

      <!-- 历史记录列表 -->
      <div v-if="historyList.length > 0" class="history-list">
        <div
          v-for="(record, index) in displayHistory"
          :key="record.id"
          class="history-item"
          :class="getHistoryItemClass(record)"
        >
          <div class="history-main">
            <div class="history-info">
              <div class="operation-title">
                <el-icon class="operation-icon" :class="getOperationIconClass(record.type)">
                  <component :is="getOperationIcon(record.type)" />
                </el-icon>
                <span class="operation-name">{{ getOperationName(record.type) }}</span>
                <el-tag :type="getStatusTagType(record.status)" size="small">
                  {{ getStatusText(record.status) }}
                </el-tag>
              </div>
              <div class="operation-details">
                <span class="detail-item">
                  <el-icon><User /></el-icon>
                  {{ record.operator || '系统' }}
                </span>
                <span class="detail-item">
                  <el-icon><Timer /></el-icon>
                  {{ formatDate(record.createdAt) }}
                </span>
                <span class="detail-item">
                  <el-icon><DataLine /></el-icon>
                  {{ record.totalCount }} 项
                </span>
                <span v-if="record.duration" class="detail-item">
                  <el-icon><Stopwatch /></el-icon>
                  {{ formatDuration(record.duration) }}
                </span>
              </div>
            </div>

            <div class="history-actions">
              <el-button
                size="small"
                text
                :icon="View"
                @click="viewDetails(record)"
              >
                详情
              </el-button>
              <el-button
                v-if="record.status === 'completed' && record.successCount > 0"
                size="small"
                text
                :icon="RefreshRight"
                @click="repeatOperation(record)"
              >
                重复
              </el-button>
              <el-button
                v-if="record.status === 'error' && record.errorCount > 0"
                size="small"
                text
                :icon="Warning"
                @click="retryOperation(record)"
              >
                重试
              </el-button>
              <el-dropdown
                trigger="click"
                @command="(command) => handleHistoryCommand(command, record)"
              >
                <el-button size="small" text :icon="MoreFilled" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="export" :icon="Download">
                      导出结果
                    </el-dropdown-item>
                    <el-dropdown-item command="copy" :icon="CopyDocument">
                      复制配置
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" :icon="Delete" divided>
                      删除记录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 操作结果统计 -->
          <div class="history-stats">
            <div class="stat-item success">
              <el-icon><CircleCheck /></el-icon>
              <span class="stat-label">成功</span>
              <span class="stat-value">{{ record.successCount || 0 }}</span>
            </div>
            <div v-if="record.errorCount > 0" class="stat-item error">
              <el-icon><CircleClose /></el-icon>
              <span class="stat-label">失败</span>
              <span class="stat-value">{{ record.errorCount }}</span>
            </div>
            <div v-if="record.skippedCount > 0" class="stat-item warning">
              <el-icon><Warning /></el-icon>
              <span class="stat-label">跳过</span>
              <span class="stat-value">{{ record.skippedCount }}</span>
            </div>
          </div>

          <!-- 展开的详细信息 -->
          <div v-if="expandedRecords.includes(record.id)" class="history-expanded">
            <el-divider />
            <div class="expanded-content">
              <div v-if="record.description" class="operation-description">
                <h4>操作描述</h4>
                <p>{{ record.description }}</p>
              </div>

              <div v-if="record.errors && record.errors.length > 0" class="operation-errors">
                <h4>错误详情</h4>
                <div class="error-list">
                  <div
                    v-for="(error, errorIndex) in record.errors.slice(0, 5)"
                    :key="errorIndex"
                    class="error-item"
                  >
                    <span class="error-index">#{{ errorIndex + 1 }}</span>
                    <span class="error-message">{{ error.message }}</span>
                  </div>
                  <div v-if="record.errors.length > 5" class="error-more">
                    还有 {{ record.errors.length - 5 }} 个错误...
                  </div>
                </div>
              </div>

              <div class="operation-config">
                <h4>操作配置</h4>
                <el-descriptions :column="2" size="small">
                  <el-descriptions-item label="规则键">{{ record.ruleKey }}</el-descriptions-item>
                  <el-descriptions-item label="操作类型">{{ getOperationName(record.type) }}</el-descriptions-item>
                  <el-descriptions-item label="总数量">{{ record.totalCount }}</el-descriptions-item>
                  <el-descriptions-item label="执行时间">{{ formatDuration(record.duration) }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <el-button
            text
            @click="loadMore"
            :loading="loadingMore"
          >
            加载更多
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-history">
        <el-empty description="暂无操作历史">
          <el-button type="primary" @click="refreshHistory">
            刷新历史
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="操作详情"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRecord" class="operation-details-dialog">
        <!-- 详情内容 -->
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作类型">
            {{ getOperationName(selectedRecord.type) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行时间">
            {{ formatDate(selectedRecord.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ formatDuration(selectedRecord.duration) }}
          </el-descriptions-item>
          <el-descriptions-item label="总数量">
            {{ selectedRecord.totalCount }}
          </el-descriptions-item>
          <el-descriptions-item label="成功数量">
            {{ selectedRecord.successCount || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 错误详情 -->
        <div v-if="selectedRecord.errors && selectedRecord.errors.length > 0" class="error-details">
          <h3>错误详情</h3>
          <el-table :data="selectedRecord.errors" stripe>
            <el-table-column prop="index" label="序号" width="80" />
            <el-table-column prop="name" label="项目名称" />
            <el-table-column prop="message" label="错误信息" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailsDialog = false">关闭</el-button>
        <el-button
          v-if="selectedRecord && selectedRecord.status === 'error'"
          type="primary"
          @click="retryFromDialog"
        >
          重试操作
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  Clock,
  RefreshRight,
  Delete,
  User,
  Timer,
  DataLine,
  Stopwatch,
  View,
  Warning,
  MoreFilled,
  Download,
  CopyDocument,
  CircleCheck,
  CircleClose,
  Check,
  Close,
  Operation,
  Upload
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '../../utils/dateUtils'

// Props
const props = defineProps({
  ruleKey: {
    type: String,
    required: true
  },
  maxRecords: {
    type: Number,
    default: 50
  }
})

// Emits
const emit = defineEmits([
  'repeat-operation',
  'retry-operation'
])

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const historyList = ref([])
const expandedRecords = ref([])
const showDetailsDialog = ref(false)
const selectedRecord = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const displayHistory = computed(() => {
  return historyList.value.slice(0, currentPage.value * pageSize.value)
})

const hasMore = computed(() => {
  return historyList.value.length > currentPage.value * pageSize.value
})

// 方法定义
const getOperationName = (type) => {
  const names = {
    activate: '批量激活',
    deactivate: '批量停用',
    delete: '批量删除',
    copy: '批量复制',
    move: '批量移动',
    export: '批量导出',
    import: '批量导入'
  }
  return names[type] || type
}

const getOperationIcon = (type) => {
  const icons = {
    activate: Check,
    deactivate: Close,
    delete: Delete,
    copy: CopyDocument,
    move: RefreshRight,
    export: Download,
    import: Upload
  }
  return icons[type] || Operation
}

const getOperationIconClass = (type) => {
  return {
    'success': type === 'activate',
    'warning': type === 'deactivate',
    'danger': type === 'delete',
    'primary': ['copy', 'move', 'export', 'import'].includes(type)
  }
}

const getStatusText = (status) => {
  const texts = {
    processing: '处理中',
    completed: '已完成',
    error: '失败',
    cancelled: '已取消',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    processing: 'primary',
    completed: 'success',
    error: 'danger',
    cancelled: 'info',
    paused: 'warning'
  }
  return types[status] || 'info'
}

const getHistoryItemClass = (record) => {
  return {
    'history-item': true,
    'expanded': expandedRecords.value.includes(record.id),
    [`status-${record.status}`]: true
  }
}

const formatDuration = (duration) => {
  if (!duration) return '-'
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`
}

const refreshHistory = async () => {
  loading.value = true
  try {
    // 模拟获取历史记录
    await new Promise(resolve => setTimeout(resolve, 500))

    // 这里应该调用实际的API获取历史记录
    historyList.value = generateMockHistory()

    ElMessage.success('历史记录已刷新')
  } catch (error) {
    ElMessage.error('刷新历史记录失败')
  } finally {
    loading.value = false
  }
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有操作历史吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    historyList.value = []
    expandedRecords.value = []
    ElMessage.success('历史记录已清空')
  } catch {
    // 用户取消
  }
}

const loadMore = () => {
  loadingMore.value = true
  setTimeout(() => {
    currentPage.value++
    loadingMore.value = false
  }, 300)
}

const viewDetails = (record) => {
  selectedRecord.value = record
  showDetailsDialog.value = true
}

const repeatOperation = (record) => {
  emit('repeat-operation', {
    type: record.type,
    config: record.config,
    items: record.items
  })
}

const retryOperation = (record) => {
  emit('retry-operation', {
    type: record.type,
    config: record.config,
    items: record.failedItems || []
  })
}

const retryFromDialog = () => {
  retryOperation(selectedRecord.value)
  showDetailsDialog.value = false
}

const handleHistoryCommand = async (command, record) => {
  switch (command) {
    case 'export':
      exportOperationResult(record)
      break
    case 'copy':
      copyOperationConfig(record)
      break
    case 'delete':
      await deleteHistoryRecord(record)
      break
  }
}

const exportOperationResult = (record) => {
  const result = {
    operation: record.type,
    timestamp: record.createdAt,
    total: record.totalCount,
    success: record.successCount,
    failed: record.errorCount,
    errors: record.errors,
    duration: record.duration
  }

  const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `operation_result_${record.id}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('操作结果已导出')
}

const copyOperationConfig = (record) => {
  const config = {
    type: record.type,
    ruleKey: record.ruleKey,
    config: record.config
  }

  navigator.clipboard.writeText(JSON.stringify(config, null, 2))
    .then(() => {
      ElMessage.success('操作配置已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

const deleteHistoryRecord = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条操作记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = historyList.value.findIndex(item => item.id === record.id)
    if (index > -1) {
      historyList.value.splice(index, 1)
      ElMessage.success('记录已删除')
    }
  } catch {
    // 用户取消
  }
}

const generateMockHistory = () => {
  // 生成模拟历史数据
  const operations = ['activate', 'deactivate', 'delete', 'copy']
  const statuses = ['completed', 'error', 'cancelled']

  return Array.from({ length: 20 }, (_, index) => {
    const type = operations[Math.floor(Math.random() * operations.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const totalCount = Math.floor(Math.random() * 100) + 1
    const successCount = status === 'completed' ? totalCount : Math.floor(Math.random() * totalCount)
    const errorCount = totalCount - successCount

    return {
      id: `history_${index + 1}`,
      type,
      status,
      ruleKey: props.ruleKey,
      operator: '管理员',
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      duration: Math.floor(Math.random() * 60000) + 1000,
      totalCount,
      successCount,
      errorCount,
      skippedCount: 0,
      description: `${getOperationName(type)}操作`,
      errors: errorCount > 0 ? Array.from({ length: Math.min(errorCount, 5) }, (_, i) => ({
        index: i,
        name: `项目${i + 1}`,
        message: '操作失败：网络错误'
      })) : [],
      config: {
        updateRelated: true,
        sendNotification: false
      }
    }
  })
}

// 添加历史记录
const addHistoryRecord = (operationData) => {
  const record = {
    id: `history_${Date.now()}`,
    type: operationData.type,
    status: operationData.status,
    ruleKey: props.ruleKey,
    operator: '当前用户',
    createdAt: new Date(),
    duration: operationData.duration,
    totalCount: operationData.totalCount,
    successCount: operationData.successCount,
    errorCount: operationData.errorCount,
    skippedCount: operationData.skippedCount || 0,
    description: operationData.description,
    errors: operationData.errors || [],
    config: operationData.config || {}
  }

  historyList.value.unshift(record)
}

// 生命周期
onMounted(() => {
  refreshHistory()
})

// 暴露方法给父组件
defineExpose({
  addHistoryRecord,
  refreshHistory
})
</script>

<style scoped>
.batch-operation-history {
  margin-top: 16px;
}

.history-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 8px;
}

.history-list {
  max-height: 500px;
  overflow-y: auto;
}

.history-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.history-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.history-item.status-completed {
  border-left: 4px solid #67c23a;
}

.history-item.status-error {
  border-left: 4px solid #f56c6c;
}

.history-item.status-cancelled {
  border-left: 4px solid #909399;
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.history-info {
  flex: 1;
}

.operation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.operation-icon {
  font-size: 16px;
}

.operation-icon.success {
  color: #67c23a;
}

.operation-icon.warning {
  color: #e6a23c;
}

.operation-icon.danger {
  color: #f56c6c;
}

.operation-icon.primary {
  color: #409eff;
}

.operation-name {
  font-weight: 600;
  color: #303133;
}

.operation-details {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.history-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.history-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-item.success {
  color: #67c23a;
}

.stat-item.error {
  color: #f56c6c;
}

.stat-item.warning {
  color: #e6a23c;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: 600;
}

.history-expanded {
  margin-top: 16px;
}

.expanded-content h4 {
  margin: 16px 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.operation-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.error-list {
  max-height: 150px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  font-size: 12px;
}

.error-index {
  color: #f56c6c;
  font-weight: 600;
  min-width: 30px;
}

.error-message {
  color: #606266;
}

.error-more {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 8px 0;
}

.load-more {
  text-align: center;
  padding: 16px 0;
}

.empty-history {
  text-align: center;
  padding: 40px 0;
}

.operation-details-dialog .error-details {
  margin-top: 20px;
}

.operation-details-dialog h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-main {
    flex-direction: column;
    gap: 12px;
  }

  .operation-details {
    flex-direction: column;
    gap: 8px;
  }

  .history-stats {
    flex-direction: column;
    gap: 8px;
  }

  .history-actions {
    align-self: stretch;
    justify-content: center;
  }
}
</style>
