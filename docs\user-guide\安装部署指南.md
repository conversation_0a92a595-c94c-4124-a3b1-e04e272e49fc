# 安装部署指南

本文档提供规则验证系统的完整安装和部署指南，适用于不同环境的部署需求。

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接（在线模式）

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 7+) / Windows 10+
- **Python**: 3.12+
- **Node.js**: 18+ (前端部署)
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **容器**: Docker 20.10+ & Docker Compose 2.0+ (推荐)

## 🚀 快速安装

### 方式一：Docker Compose 部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd rule_validation_system
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等参数
```

3. **启动服务**
```bash
# 主节点部署
docker-compose -f docker-compose.master.yml up -d

# 从节点部署
docker-compose -f docker-compose.slave.yml up -d
```

### 方式二：源码部署

1. **安装依赖**
```bash
# 后端依赖
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

2. **数据库初始化**
```bash
# 运行数据库迁移
alembic upgrade head
```

3. **启动服务**
```bash
# 启动主节点
python master.py

# 启动从节点
python slave.py

# 启动前端（开发模式）
cd frontend
npm run dev
```

## ⚙️ 配置说明

### 环境变量配置

主要配置参数：

```env
# 应用模式
MODE=master  # 或 slave

# 数据库配置
DATABASE_URL=mysql://user:password@host:port/database

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=18001  # 主节点端口，从节点使用18002

# 同步配置（从节点）
ENABLE_RULE_SYNC=true
MASTER_API_ENDPOINT=http://master-node:18001
SLAVE_API_KEY=your_secure_api_key

# 日志配置
LOG_LEVEL=INFO
LOG_STDOUT_ENABLED=true
```

详细配置说明请参考 [配置说明.md](配置说明.md)

## 🔧 部署模式

### 主节点部署
- 包含完整的Web界面和API服务
- 连接数据库，管理规则数据
- 提供规则同步服务给从节点

### 从节点部署
- 仅包含规则验证API服务
- 支持在线同步和离线模式
- 高性能规则验证处理

### 离线部署
适用于完全封闭的内网环境，详细说明请参考：
- [离线部署指南](../operations/deployment/离线部署指南.md)

## ✅ 部署验证

### 健康检查
```bash
# 检查主节点状态
curl http://localhost:18001/health

# 检查从节点状态
curl http://localhost:18002/health
```

### 功能验证
1. **Web界面访问**: http://localhost:18001
2. **API接口测试**: 使用Postman或curl测试API
3. **规则验证测试**: 提交测试数据验证规则引擎

## 🆘 常见问题

### 启动失败
1. 检查端口是否被占用
2. 验证数据库连接配置
3. 查看应用日志排查错误

### 性能问题
1. 调整worker进程数量
2. 优化数据库连接池配置
3. 检查系统资源使用情况

### 网络问题
1. 检查防火墙设置
2. 验证服务间网络连通性
3. 确认API密钥配置正确

更多问题解决方案请参考 [常见问题解决.md](常见问题解决.md)

## 📞 技术支持

如需技术支持，请提供：
1. 系统环境信息
2. 配置文件内容
3. 错误日志信息
4. 问题复现步骤

---

**更新时间**: 2025-07-23  
**适用版本**: v2.0+
