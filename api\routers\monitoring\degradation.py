"""
降级监控和管理API路由
提供降级状态查询、手动控制、历史查询、指标统计等功能
"""

import time
from typing import Any

from fastapi import APIRouter, Query, Request
from pydantic import BaseModel, Field

from api.dependencies.auth import get_api_key_dependency
from core.constants.error_codes import ErrorCodes
from core.degradation_adapters import get_adapter_manager
from core.degradation_core import DegradationLevel
from core.degradation_manager import get_degradation_manager
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from models.api import ApiResponse

# 降级监控路由（公开访问，用于状态查询）
degradation_monitoring_router = APIRouter(
    prefix="/api/v1/degradation",
    tags=["Degradation Monitoring"],
)

# 降级管理路由（需要API密钥认证，用于控制操作）
degradation_management_router = APIRouter(
    prefix="/api/v1/degradation",
    tags=["Degradation Management (Secure)"],
    dependencies=[get_api_key_dependency()],
)


class DegradationStatusResponse(BaseModel):
    """降级状态响应"""

    current_level: str = Field(..., description="当前降级级别")
    previous_level: str = Field(..., description="前一个降级级别")
    is_degraded: bool = Field(..., description="是否处于降级状态")
    last_change_time: float = Field(..., description="最后变更时间戳")
    degradation_duration: float = Field(..., description="降级持续时间（秒）")
    active_triggers: list[str] = Field(default_factory=list, description="活跃的触发器")
    is_manual_override: bool = Field(default=False, description="是否手动干预")
    override_reason: str | None = Field(None, description="手动干预原因")
    executed_actions_count: int = Field(default=0, description="已执行动作数量")
    enabled: bool = Field(..., description="降级机制是否启用")
    running: bool = Field(..., description="降级管理器是否运行中")


class ComponentStatusResponse(BaseModel):
    """组件状态响应"""

    component_name: str = Field(..., description="组件名称")
    current_level: str = Field(..., description="当前降级级别")
    is_degraded: bool = Field(..., description="是否处于降级状态")
    last_change_time: float = Field(..., description="最后变更时间戳")
    degradation_reason: str = Field(..., description="降级原因")
    enabled: bool = Field(..., description="适配器是否启用")
    original_config: dict[str, Any] = Field(default_factory=dict, description="原始配置")
    degraded_config: dict[str, Any] = Field(default_factory=dict, description="降级配置")


class DegradationMetricsResponse(BaseModel):
    """降级指标响应"""

    total_degradations: int = Field(default=0, description="总降级次数")
    successful_degradations: int = Field(default=0, description="成功降级次数")
    failed_degradations: int = Field(default=0, description="失败降级次数")
    total_recoveries: int = Field(default=0, description="总恢复次数")
    successful_recoveries: int = Field(default=0, description="成功恢复次数")
    failed_recoveries: int = Field(default=0, description="失败恢复次数")
    total_actions_executed: int = Field(default=0, description="总执行动作数")
    successful_actions: int = Field(default=0, description="成功执行动作数")
    failed_actions: int = Field(default=0, description="失败执行动作数")
    average_degradation_duration: float = Field(default=0.0, description="平均降级持续时间")
    current_uptime: float = Field(default=0.0, description="当前运行时间")
    last_degradation_time: float | None = Field(None, description="最后降级时间")
    degradation_success_rate: float = Field(default=0.0, description="降级成功率")
    recovery_success_rate: float = Field(default=0.0, description="恢复成功率")


class DegradationEventResponse(BaseModel):
    """降级事件响应"""

    event_type: str = Field(..., description="事件类型")
    timestamp: float = Field(..., description="事件时间戳")
    level: str = Field(..., description="降级级别")
    trigger_type: str | None = Field(None, description="触发类型")
    trigger_value: float | None = Field(None, description="触发值")
    metadata: dict[str, Any] = Field(default_factory=dict, description="事件元数据")
    actions_count: int = Field(default=0, description="执行动作数量")


class ManualControlRequest(BaseModel):
    """手动控制请求"""

    level: str = Field(..., description="目标降级级别")
    reason: str = Field(..., description="操作原因")
    force: bool = Field(default=False, description="是否强制执行")


@degradation_monitoring_router.get("/status", response_model=ApiResponse[DegradationStatusResponse])
async def get_degradation_status(request: Request):
    """
    获取当前降级状态

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[DegradationStatusResponse]: 当前降级状态
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation status: {request_id}")

        # 添加请求追踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_status_request",
                {"endpoint": "/api/v1/degradation/status"},
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取当前状态
        current_status = degradation_manager.get_current_status()

        # 构建响应
        status_response = DegradationStatusResponse(
            current_level=current_status["current_level"],
            previous_level=current_status["previous_level"],
            is_degraded=current_status["is_degraded"],
            last_change_time=current_status["last_change_time"],
            degradation_duration=current_status["degradation_duration"],
            active_triggers=current_status["active_triggers"],
            is_manual_override=current_status["is_manual_override"],
            override_reason=current_status["override_reason"],
            executed_actions_count=current_status["executed_actions_count"],
            enabled=degradation_manager.is_enabled(),
            running=degradation_manager.is_running(),
        )

        # 添加成功事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_status_success",
                {"current_level": status_response.current_level, "is_degraded": status_response.is_degraded},
            )

        return ApiResponse.success_response(
            data=status_response,
            message="降级状态获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation status: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_status_error",
                {"error": str(e), "error_type": type(e).__name__},
            )

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级状态失败",
            request_id=request_id,
        )


@degradation_monitoring_router.get("/components", response_model=ApiResponse[list[ComponentStatusResponse]])
async def get_components_status(request: Request):
    """
    获取各组件降级状态

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[List[ComponentStatusResponse]]: 组件状态列表
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting components status: {request_id}")

        # 获取适配器管理器
        adapter_manager = get_adapter_manager()

        # 获取所有适配器状态
        adapters_status = adapter_manager.get_adapters_status()

        # 构建响应
        components_response = []
        for component_name, status in adapters_status.items():
            component_response = ComponentStatusResponse(
                component_name=component_name,
                current_level=status["current_level"],
                is_degraded=status["is_degraded"],
                last_change_time=status["last_change_time"],
                degradation_reason=status["degradation_reason"],
                enabled=status["enabled"],
                original_config=status["original_config"],
                degraded_config=status["degraded_config"],
            )
            components_response.append(component_response)

        return ApiResponse.success_response(
            data=components_response,
            message="组件状态获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get components status: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取组件状态失败",
            request_id=request_id,
        )


@degradation_monitoring_router.get("/metrics", response_model=ApiResponse[DegradationMetricsResponse])
async def get_degradation_metrics(request: Request):
    """
    获取降级统计指标

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[DegradationMetricsResponse]: 降级统计指标
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation metrics: {request_id}")

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取指标数据
        metrics = degradation_manager.get_metrics()

        # 构建响应
        metrics_response = DegradationMetricsResponse(
            total_degradations=metrics.get("total_degradations", 0),
            successful_degradations=metrics.get("successful_degradations", 0),
            failed_degradations=metrics.get("failed_degradations", 0),
            total_recoveries=metrics.get("total_recoveries", 0),
            successful_recoveries=metrics.get("successful_recoveries", 0),
            failed_recoveries=metrics.get("failed_recoveries", 0),
            total_actions_executed=metrics.get("total_actions_executed", 0),
            successful_actions=metrics.get("successful_actions", 0),
            failed_actions=metrics.get("failed_actions", 0),
            average_degradation_duration=metrics.get("average_degradation_duration", 0.0),
            current_uptime=metrics.get("current_uptime", 0.0),
            last_degradation_time=metrics.get("last_degradation_time"),
            degradation_success_rate=metrics.get("degradation_success_rate", 0.0),
            recovery_success_rate=metrics.get("recovery_success_rate", 0.0),
        )

        return ApiResponse.success_response(
            data=metrics_response,
            message="降级指标获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation metrics: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级指标失败",
            request_id=request_id,
        )


@degradation_monitoring_router.get("/events", response_model=ApiResponse[list[DegradationEventResponse]])
async def get_degradation_events(
    request: Request,
    limit: int = Query(50, ge=1, le=500, description="返回记录数量限制"),
    event_types: str | None = Query(None, description="事件类型过滤（逗号分隔）"),
    start_time: float | None = Query(None, description="开始时间戳"),
    end_time: float | None = Query(None, description="结束时间戳"),
):
    """
    获取降级事件日志

    Args:
        request: HTTP请求对象
        limit: 返回记录数量限制
        event_types: 事件类型过滤
        start_time: 开始时间戳
        end_time: 结束时间戳

    Returns:
        ApiResponse[List[DegradationEventResponse]]: 降级事件列表
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation events: {request_id}")

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取事件历史
        events = degradation_manager.get_event_history(limit)

        # 过滤事件类型
        if event_types:
            filter_types = set(event_types.split(","))
            events = [event for event in events if event.event_type.value in filter_types]

        # 过滤时间范围
        if start_time:
            events = [event for event in events if event.timestamp >= start_time]
        if end_time:
            events = [event for event in events if event.timestamp <= end_time]

        # 转换事件格式
        events_response = []
        for event in events:
            event_response = DegradationEventResponse(
                event_type=event.event_type.value,
                timestamp=event.timestamp,
                level=event.level.value,
                trigger_type=event.trigger_type.value if event.trigger_type else None,
                trigger_value=event.trigger_value,
                metadata=event.metadata,
                actions_count=len(event.actions),
            )
            events_response.append(event_response)

        return ApiResponse.success_response(
            data=events_response,
            message="降级事件获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation events: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级事件失败",
            request_id=request_id,
        )


@degradation_monitoring_router.get("/history", response_model=ApiResponse[list[dict[str, Any]]])
async def get_degradation_history(
    request: Request,
    limit: int = Query(50, ge=1, le=500, description="返回记录数量限制"),
    start_time: float | None = Query(None, description="开始时间戳"),
    end_time: float | None = Query(None, description="结束时间戳"),
):
    """
    获取降级历史记录

    Args:
        request: HTTP请求对象
        limit: 返回记录数量限制
        start_time: 开始时间戳
        end_time: 结束时间戳

    Returns:
        ApiResponse[List[Dict[str, Any]]]: 降级历史记录
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation history: {request_id}")

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取历史记录
        history = degradation_manager.get_degradation_history(limit)

        # 过滤时间范围
        if start_time or end_time:
            filtered_history = []
            for record in history:
                timestamp = record.get("timestamp", 0)
                if start_time and timestamp < start_time:
                    continue
                if end_time and timestamp > end_time:
                    continue
                filtered_history.append(record)
            history = filtered_history

        return ApiResponse.success_response(
            data=history,
            message="降级历史获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation history: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级历史失败",
            request_id=request_id,
        )


@degradation_monitoring_router.get("/performance", response_model=ApiResponse[dict[str, Any]])
async def get_degradation_performance_impact(request: Request):
    """
    获取降级对性能的影响

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[Dict[str, Any]]: 性能影响数据
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation performance impact: {request_id}")

        # 获取降级管理器和适配器管理器
        degradation_manager = get_degradation_manager()
        adapter_manager = get_adapter_manager()

        # 获取当前状态
        current_status = degradation_manager.get_current_status()
        components_status = adapter_manager.get_adapters_status()

        # 构建性能影响数据
        performance_impact = {
            "overall_status": {
                "current_level": current_status["current_level"],
                "is_degraded": current_status["is_degraded"],
                "degradation_duration": current_status["degradation_duration"],
            },
            "components_impact": {},
            "estimated_performance_reduction": 0.0,
            "resource_savings": {},
            "timestamp": time.time(),
        }

        # 计算各组件的性能影响
        total_reduction = 0.0
        component_count = 0

        for component_name, status in components_status.items():
            if status["is_degraded"]:
                component_count += 1

                # 根据降级级别估算性能影响
                level = status["current_level"]
                if level == "light_degradation":
                    reduction = 0.25  # 25%性能降低
                elif level == "moderate_degradation":
                    reduction = 0.50  # 50%性能降低
                elif level == "severe_degradation":
                    reduction = 0.75  # 75%性能降低
                else:
                    reduction = 0.0

                total_reduction += reduction

                performance_impact["components_impact"][component_name] = {
                    "level": level,
                    "estimated_reduction": reduction,
                    "degraded_config": status["degraded_config"],
                }

        # 计算整体性能影响
        if component_count > 0:
            performance_impact["estimated_performance_reduction"] = total_reduction / component_count

        # 估算资源节省
        if current_status["is_degraded"]:
            performance_impact["resource_savings"] = {
                "cpu_usage_reduction": performance_impact["estimated_performance_reduction"] * 0.3,
                "memory_usage_reduction": performance_impact["estimated_performance_reduction"] * 0.2,
                "network_usage_reduction": performance_impact["estimated_performance_reduction"] * 0.1,
            }

        return ApiResponse.success_response(
            data=performance_impact,
            message="性能影响数据获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get performance impact: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取性能影响数据失败",
            request_id=request_id,
        )


# 手动控制API（需要认证）
@degradation_management_router.post("/trigger", response_model=ApiResponse[dict[str, Any]])
async def manual_trigger_degradation(request: Request, control_request: ManualControlRequest):
    """
    手动触发降级

    Args:
        request: HTTP请求对象
        control_request: 手动控制请求

    Returns:
        ApiResponse[Dict[str, Any]]: 操作结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(
            f"Manual degradation trigger: {request_id}",
            extra={
                "target_level": control_request.level,
                "reason": control_request.reason,
                "force": control_request.force,
            },
        )

        # 验证降级级别
        try:
            target_level = DegradationLevel(control_request.level)
        except ValueError:
            return ApiResponse.error_response(
                code=ErrorCodes.INVALID_REQUEST_PARAMETER,
                message=f"无效的降级级别: {control_request.level}",
                request_id=request_id,
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 检查是否已经处于目标级别
        current_status = degradation_manager.get_current_status()
        if current_status["current_level"] == control_request.level and not control_request.force:
            return ApiResponse.success_response(
                data={
                    "success": True,
                    "message": f"已经处于目标降级级别: {control_request.level}",
                    "current_level": current_status["current_level"],
                    "timestamp": time.time(),
                },
                message="无需执行降级操作",
                request_id=request_id,
            )

        # 执行手动降级
        success = degradation_manager.manual_trigger_degradation(target_level, control_request.reason)

        if success:
            # 获取更新后的状态
            updated_status = degradation_manager.get_current_status()

            result = {
                "success": True,
                "current_level": updated_status["current_level"],
                "previous_level": updated_status["previous_level"],
                "reason": control_request.reason,
                "timestamp": time.time(),
                "executed_actions": updated_status["executed_actions_count"],
            }

            return ApiResponse.success_response(
                data=result,
                message="手动降级执行成功",
                request_id=request_id,
            )
        else:
            return ApiResponse.error_response(
                code=ErrorCodes.OPERATION_FAILED,
                message="手动降级执行失败",
                request_id=request_id,
            )

    except Exception as e:
        logger.error(f"Failed to trigger manual degradation: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="手动降级执行异常",
            request_id=request_id,
        )


@degradation_management_router.post("/recover", response_model=ApiResponse[dict[str, Any]])
async def manual_recover_degradation(request: Request, reason: str = "Manual recovery from management API"):
    """
    手动恢复正常状态

    Args:
        request: HTTP请求对象
        reason: 恢复原因

    Returns:
        ApiResponse[Dict[str, Any]]: 操作结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Manual degradation recovery: {request_id}", extra={"reason": reason})

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 检查当前状态
        current_status = degradation_manager.get_current_status()
        if current_status["current_level"] == "normal":
            return ApiResponse.success_response(
                data={
                    "success": True,
                    "message": "系统已经处于正常状态",
                    "current_level": current_status["current_level"],
                    "timestamp": time.time(),
                },
                message="无需执行恢复操作",
                request_id=request_id,
            )

        # 执行手动恢复
        success = degradation_manager.manual_recover(reason)

        if success:
            # 获取更新后的状态
            updated_status = degradation_manager.get_current_status()

            result = {
                "success": True,
                "current_level": updated_status["current_level"],
                "previous_level": updated_status["previous_level"],
                "reason": reason,
                "timestamp": time.time(),
                "recovery_duration": time.time() - current_status["last_change_time"],
            }

            return ApiResponse.success_response(
                data=result,
                message="手动恢复执行成功",
                request_id=request_id,
            )
        else:
            return ApiResponse.error_response(
                code=ErrorCodes.OPERATION_FAILED,
                message="手动恢复执行失败",
                request_id=request_id,
            )

    except Exception as e:
        logger.error(f"Failed to recover from degradation: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="手动恢复执行异常",
            request_id=request_id,
        )


class LevelSetRequest(BaseModel):
    """设置级别请求"""

    level: str = Field(..., description="目标降级级别")
    reason: str = Field(..., description="设置原因")
    duration: int | None = Field(None, description="持续时间（秒），None表示永久")


@degradation_management_router.post("/level", response_model=ApiResponse[dict[str, Any]])
async def set_degradation_level(request: Request, level_request: LevelSetRequest):
    """
    设置指定降级级别

    Args:
        request: HTTP请求对象
        level_request: 级别设置请求

    Returns:
        ApiResponse[Dict[str, Any]]: 操作结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(
            f"Setting degradation level: {request_id}",
            extra={
                "target_level": level_request.level,
                "reason": level_request.reason,
                "duration": level_request.duration,
            },
        )

        # 验证降级级别
        try:
            target_level = DegradationLevel(level_request.level)
        except ValueError:
            return ApiResponse.error_response(
                code=ErrorCodes.INVALID_REQUEST_PARAMETER,
                message=f"无效的降级级别: {level_request.level}",
                request_id=request_id,
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 执行级别设置
        if level_request.level == "normal":
            success = degradation_manager.manual_recover(level_request.reason)
        else:
            success = degradation_manager.manual_trigger_degradation(target_level, level_request.reason)

        if success:
            # 获取更新后的状态
            updated_status = degradation_manager.get_current_status()

            result = {
                "success": True,
                "current_level": updated_status["current_level"],
                "previous_level": updated_status["previous_level"],
                "reason": level_request.reason,
                "timestamp": time.time(),
                "duration": level_request.duration,
            }

            # 如果设置了持续时间，可以在这里添加定时恢复逻辑
            if level_request.duration and level_request.level != "normal":
                # 这里可以添加定时任务来自动恢复
                result["auto_recovery_scheduled"] = True
                result["recovery_time"] = time.time() + level_request.duration

            return ApiResponse.success_response(
                data=result,
                message="降级级别设置成功",
                request_id=request_id,
            )
        else:
            return ApiResponse.error_response(
                code=ErrorCodes.OPERATION_FAILED,
                message="降级级别设置失败",
                request_id=request_id,
            )

    except Exception as e:
        logger.error(f"Failed to set degradation level: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="降级级别设置异常",
            request_id=request_id,
        )
