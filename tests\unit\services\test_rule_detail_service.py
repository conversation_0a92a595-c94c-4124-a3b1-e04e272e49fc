"""
RuleDetailService单元测试
测试规则明细服务的核心功能
"""

from unittest.mock import Mock, patch

import pytest
from sqlalchemy.orm import Session

from models.database import FieldTypeEnum, RuleDetail, RuleDetailStatusEnum, RuleFieldMetadata, RuleTemplate
from services.rule_detail_service import RuleDetailService


class TestRuleDetailService:
    """测试RuleDetailService类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_template(self):
        """模拟规则模板"""
        template = Mock(spec=RuleTemplate)
        template.rule_key = "test_rule"
        template.rule_type = "测试类型"
        template.name = "测试规则模板"

        # 模拟字段元数据
        field_metadata = Mock(spec=RuleFieldMetadata)
        field_metadata.field_name = "test_field"
        field_metadata.field_type = FieldTypeEnum.STRING
        field_metadata.is_required = True
        field_metadata.display_name = "测试字段"
        field_metadata.validate_field_value.return_value = {"valid": True, "errors": [], "warnings": []}

        template.get_field_metadata_list.return_value = [field_metadata]
        return template

    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        with patch("services.rule_detail_service.FieldMappingManager"), \
            patch("services.rule_detail_service.UnifiedDataMappingEngine") as mock_engine:

            # 配置模拟的数据映射引擎
            mock_engine_instance = mock_engine.return_value
            mock_engine_instance.normalize_field_names.return_value = {
                "rule_id": "test_001",
                "rule_key": "test_rule",
                "rule_name": "测试规则",
                "level1": "一级错误",
                "level2": "二级错误",
                "level3": "三级错误",
                "error_reason": "测试错误原因",
                "degree": "轻微",
                "reference": "测试参考",
                "detail_position": "测试位置",
                "prompted_fields1": "test_field",
                "type": "测试类型",
                "pos": "测试业务",
                "applicableArea": "全国",
                "default_use": "是",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
            mock_engine_instance.validate_data.return_value = {"valid": True, "errors": [], "warnings": []}
            mock_engine_instance.separate_fields.return_value = (
                {  # 固定字段
                    "rule_id": "test_001",
                    "rule_key": "test_rule",
                    "rule_name": "测试规则",
                    "level1": "一级错误",
                    "level2": "二级错误",
                    "level3": "三级错误",
                    "error_reason": "测试错误原因",
                    "degree": "轻微",
                    "reference": "测试参考",
                    "detail_position": "测试位置",
                    "prompted_fields1": "test_field",
                    "type": "测试类型",
                    "pos": "测试业务",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31"
                },
                {}  # 扩展字段
            )

            return RuleDetailService(mock_session)

    def test_create_rule_detail_success(self, service, mock_session, mock_template):
        """测试成功创建规则明细"""
        # 配置模拟
        service._get_template_by_key_cached = Mock(return_value=mock_template)
        service._get_field_metadata_cached = Mock(return_value=[])
        service._validate_rule_data = Mock(return_value=Mock(valid=True, data={
            "rule_id": "test_001",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "level1": "一级错误",
            "level2": "二级错误",
            "level3": "三级错误",
            "error_reason": "测试错误原因",
            "degree": "轻微",
            "reference": "测试参考",
            "detail_position": "测试位置",
            "prompted_fields1": "test_field",
            "type": "测试类型",
            "pos": "测试业务",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }))

        # 测试数据
        data = {
            "rule_id": "test_001",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "level1": "一级错误",
            "level2": "二级错误",
            "level3": "三级错误",
            "error_reason": "测试错误原因",
            "degree": "轻微",
            "reference": "测试参考",
            "detail_position": "测试位置",
            "prompted_fields1": "test_field",
            "type": "测试类型",
            "pos": "测试业务",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }

        # 执行测试
        result = service.create_rule_detail("test_rule", data)

        # 验证结果
        assert isinstance(result, RuleDetail)
        assert result.rule_id == "test_001"
        assert result.rule_key == "test_rule"
        assert result.rule_name == "测试规则"

        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()

    def test_create_rule_detail_template_not_found(self, service, mock_session):
        """测试规则模板不存在的情况"""
        # 配置模拟
        service._get_template_by_key_cached = Mock(return_value=None)

        # 使用非空数据避免输入验证失败
        test_data = {"rule_id": "test_001", "rule_name": "测试规则"}

        # 执行测试并验证异常
        with pytest.raises(Exception, match="规则模板 'test_rule' 不存在"):
            service.create_rule_detail("test_rule", test_data)

    def test_create_rule_detail_validation_failed(self, service, mock_session, mock_template):
        """测试数据验证失败的情况"""
        # 配置模拟
        service._get_template_by_key_cached = Mock(return_value=mock_template)
        service._get_field_metadata_cached = Mock(return_value=[])
        service._validate_rule_data = Mock(return_value=Mock(valid=False, errors=["字段验证失败"]))

        # 使用非空数据避免输入验证失败
        test_data = {"rule_id": "test_001", "rule_name": "测试规则"}

        # 执行测试并验证异常
        with pytest.raises(Exception, match="数据验证失败"):
            service.create_rule_detail("test_rule", test_data)

    def test_get_rule_detail_success(self, service, mock_session):
        """测试成功获取规则明细"""
        # 创建模拟的规则明细
        mock_rule_detail = Mock(spec=RuleDetail)
        mock_rule_detail.rule_id = "test_001"
        mock_rule_detail.rule_key = "test_rule"

        # 配置查询结果
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_rule_detail
        mock_session.query.return_value = mock_query

        # 执行测试
        result = service.get_rule_detail("test_rule", "test_001")

        # 验证结果
        assert result == mock_rule_detail
        mock_session.query.assert_called_once_with(RuleDetail)

    def test_get_rule_detail_not_found(self, service, mock_session):
        """测试规则明细不存在的情况"""
        # 配置查询结果
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        mock_session.query.return_value = mock_query

        # 执行测试
        result = service.get_rule_detail("test_rule", "test_001")

        # 验证结果
        assert result is None

    def test_update_rule_detail_success(self, service, mock_session, mock_template):
        """测试成功更新规则明细"""
        # 创建模拟的现有规则明细
        mock_rule_detail = Mock(spec=RuleDetail)
        mock_rule_detail.rule_id = "test_001"
        mock_rule_detail.rule_key = "test_rule"
        mock_rule_detail.merge_from_dict = Mock()
        mock_rule_detail.validate_data.return_value = {"valid": True, "errors": [], "warnings": []}

        # 配置模拟
        service.get_rule_detail = Mock(return_value=mock_rule_detail)
        service._get_template_by_key = Mock(return_value=mock_template)

        # 测试数据
        update_data = {"rule_name": "更新后的规则名称"}

        # 执行测试
        result = service.update_rule_detail("test_rule", "test_001", update_data)

        # 验证结果
        assert result == mock_rule_detail
        mock_rule_detail.merge_from_dict.assert_called_once()
        mock_session.flush.assert_called_once()

    def test_update_rule_detail_not_found(self, service, mock_session):
        """测试更新不存在的规则明细"""
        # 配置模拟
        service.get_rule_detail = Mock(return_value=None)

        # 执行测试并验证异常
        with pytest.raises(Exception, match="规则明细不存在"):
            service.update_rule_detail("test_rule", "test_001", {})

    def test_delete_rule_detail_success(self, service, mock_session):
        """测试成功删除规则明细"""
        # 创建模拟的规则明细
        mock_rule_detail = Mock(spec=RuleDetail)

        # 配置模拟
        service.get_rule_detail = Mock(return_value=mock_rule_detail)

        # 执行测试
        result = service.delete_rule_detail("test_rule", "test_001")

        # 验证结果
        assert result is True
        mock_session.delete.assert_called_once_with(mock_rule_detail)
        mock_session.flush.assert_called_once()

    def test_delete_rule_detail_not_found(self, service, mock_session):
        """测试删除不存在的规则明细"""
        # 配置模拟
        service.get_rule_detail = Mock(return_value=None)

        # 执行测试
        result = service.delete_rule_detail("test_rule", "test_001")

        # 验证结果
        assert result is False
        mock_session.delete.assert_not_called()

    def test_list_rule_details(self, service, mock_session):
        """测试查询规则明细列表"""
        # 创建模拟的规则明细列表
        mock_rule_details = [Mock(spec=RuleDetail) for _ in range(3)]

        # 配置查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 10
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_rule_details
        mock_session.query.return_value = mock_query

        # 执行测试
        result_list, total = service.list_rule_details("test_rule", page=1, page_size=5)

        # 验证结果
        assert len(result_list) == 3
        assert total == 10
        mock_session.query.assert_called_once_with(RuleDetail)

    def test_batch_create_rule_details(self, service, mock_session, mock_template):
        """测试批量创建规则明细"""
        # 配置模拟
        service._get_template_by_key = Mock(return_value=mock_template)

        # 测试数据
        data_list = [
            {
                "rule_id": "test_001",
                "rule_key": "test_rule",
                "rule_name": "测试规则1",
                "level1": "一级错误",
                "level2": "二级错误",
                "level3": "三级错误",
                "error_reason": "测试错误原因",
                "degree": "轻微",
                "reference": "测试参考",
                "detail_position": "测试位置",
                "prompted_fields1": "test_field",
                "type": "测试类型",
                "pos": "测试业务",
                "applicableArea": "全国",
                "default_use": "是",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            },
            {
                "rule_id": "test_002",
                "rule_key": "test_rule",
                "rule_name": "测试规则2",
                "level1": "一级错误",
                "level2": "二级错误",
                "level3": "三级错误",
                "error_reason": "测试错误原因",
                "degree": "轻微",
                "reference": "测试参考",
                "detail_position": "测试位置",
                "prompted_fields1": "test_field",
                "type": "测试类型",
                "pos": "测试业务",
                "applicableArea": "全国",
                "default_use": "是",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
        ]

        # 执行测试
        result = service.batch_create_rule_details("test_rule", data_list)

        # 验证结果
        assert len(result) == 2
        assert all(isinstance(detail, RuleDetail) for detail in result)
        mock_session.add_all.assert_called_once()
        mock_session.flush.assert_called_once()

    def test_get_rule_detail_statistics(self, service, mock_session):
        """测试获取规则明细统计信息"""
        from datetime import datetime

        # 配置模拟查询结果
        # 设置不同的返回值给不同的查询
        mock_query_results = [
            100,
            [(RuleDetailStatusEnum.ACTIVE, 80), (RuleDetailStatusEnum.INACTIVE, 20)], 
            [("一级错误", 60), ("二级错误", 40)],
            datetime.now()
        ]

        def side_effect(*args, **kwargs):
            return mock_query_results.pop(0) if mock_query_results else None

        mock_session.query.return_value.filter.return_value.scalar.side_effect = side_effect
        mock_session.query.return_value.filter.return_value.group_by.return_value.all.side_effect = side_effect

        # 执行测试
        stats = service.get_rule_detail_statistics("test_rule")

        # 验证结果
        assert stats["rule_key"] == "test_rule"
        assert stats["total_count"] == 100
        assert "status_distribution" in stats
        assert "level1_distribution" in stats

    def test_create_rule_detail_input_validation(self, service, mock_session):
        """测试输入参数验证功能"""
        from services.rule_detail_service import ServiceError

        # 测试空rule_key
        with pytest.raises(ServiceError, match="规则模板键不能为空"):
            service.create_rule_detail("", {"data": "test"})

        # 测试非字符串rule_key
        with pytest.raises(ServiceError, match="规则模板键不能为空且必须是字符串类型"):
            service.create_rule_detail(123, {"data": "test"})

        # 测试非字典类型data
        with pytest.raises(ServiceError, match="规则明细数据必须是字典类型"):
            service.create_rule_detail("test_rule", "not_dict")

        # 测试空数据
        with pytest.raises(ServiceError, match="规则明细数据不能为空"):
            service.create_rule_detail("test_rule", {})
