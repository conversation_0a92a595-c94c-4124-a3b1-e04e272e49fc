# API 函数使用指南

## 📁 模块结构

```
frontend/src/api/
├── request.js          # HTTP请求基础封装
├── rules.js           # 基础规则管理API
├── ruleDetails.js     # 规则明细管理API (新增)
└── README.md          # 本文档
```

## 🔧 基础规则管理 API

**文件**: `rules.js`

**用途**: 处理规则的基础CRUD操作、状态管理、验证等

**主要函数**:
```javascript
import { 
  getRulesStatus,
  getRuleDetail,
  createRule,
  updateRule,
  deleteRule,
  validateRuleData,
  getRuleDependencies
} from './rules'
```

## 📋 规则明细管理 API

**文件**: `ruleDetails.js`

**用途**: 专门处理规则明细的CRUD操作、批量处理、统计分析等

### 基础CRUD操作
```javascript
import { 
  getRuleDetailsList,
  getRuleDetailById,
  createRuleDetail,
  updateRuleDetail,
  deleteRuleDetail
} from './ruleDetails'

// 获取规则明细列表
const details = await getRuleDetailsList('rule-001', {
  page: 1,
  page_size: 20,
  status: 'ACTIVE'
})

// 获取单条规则明细
const detail = await getRuleDetailById('rule-001', 'detail-123')

// 创建规则明细
const newDetail = await createRuleDetail('rule-001', {
  rule_detail_id: 'detail-456',
  rule_name: '新规则明细',
  status: 'ACTIVE'
})
```

### 批量操作
```javascript
import { 
  batchOperateRuleDetails,
  batchCreateRuleDetails,
  batchUpdateRuleDetails,
  batchDeleteRuleDetails,
  incrementalUploadRuleDetails
} from './ruleDetails'

// 批量创建
await batchCreateRuleDetails('rule-001', [
  { rule_detail_id: 'detail-1', rule_name: '规则1' },
  { rule_detail_id: 'detail-2', rule_name: '规则2' }
])

// 批量更新
await batchUpdateRuleDetails('rule-001', [
  { id: 1, data: { rule_name: '更新后的规则1' } },
  { id: 2, data: { rule_name: '更新后的规则2' } }
])

// 增量上传
await incrementalUploadRuleDetails('rule-001', {
  operations: [
    { action: 'CREATE', data: { rule_detail_id: 'new-1', rule_name: '新规则' } },
    { action: 'UPDATE', id: 1, data: { rule_name: '更新规则' } },
    { action: 'DELETE', id: 2 }
  ]
})
```

### 查询和搜索
```javascript
import { 
  searchRuleDetails,
  queryRuleDetails
} from './ruleDetails'

// 搜索规则明细
const searchResults = await searchRuleDetails('rule-001', {
  keyword: '测试',
  fields: ['rule_name', 'error_reason'],
  filters: {
    status: ['ACTIVE'],
    error_levels: ['ERROR', 'WARNING']
  }
})

// 高级查询
const queryResults = await queryRuleDetails('rule-001', {
  conditions: [
    { field: 'status', operator: 'eq', value: 'ACTIVE' },
    { field: 'created_at', operator: 'gte', value: '2024-01-01' }
  ],
  sort: [{ field: 'created_at', order: 'desc' }]
})
```

### 统计和分析
```javascript
import { 
  getRuleDetailsStatistics,
  getRuleDetailsDistribution
} from './ruleDetails'

// 获取统计信息
const stats = await getRuleDetailsStatistics('rule-001')
// 返回: { total_count, status_distribution, error_level_distribution, ... }

// 获取分布统计
const distribution = await getRuleDetailsDistribution('rule-001', 'status')
// 返回: { ACTIVE: 100, INACTIVE: 20, DELETED: 5 }
```

### 导入导出
```javascript
import { 
  exportRuleDetails,
  downloadRuleDetailsTemplate,
  previewRuleDetailsImport
} from './ruleDetails'

// 导出规则明细
const exportResult = await exportRuleDetails('rule-001', {
  format: 'excel',
  filters: { status: 'ACTIVE' },
  fields: ['rule_name', 'error_level_1', 'status']
})

// 下载模板
const template = await downloadRuleDetailsTemplate('rule-001')

// 预览导入
const preview = await previewRuleDetailsImport('rule-001', importData)
```

### 验证和校验
```javascript
import { 
  validateRuleDetailsData,
  getRuleDetailsFieldConfig,
  getRuleDetailsEnumValues
} from './ruleDetails'

// 验证数据
const validation = await validateRuleDetailsData('rule-001', [
  { rule_detail_id: 'test-1', rule_name: '测试规则' }
])

// 获取字段配置
const fieldConfig = await getRuleDetailsFieldConfig('rule-001')

// 获取枚举值
const enumValues = await getRuleDetailsEnumValues('rule-001', 'status')
```

### 历史和审计
```javascript
import { 
  getRuleDetailHistory,
  getRuleDetailsAuditLog
} from './ruleDetails'

// 获取变更历史
const history = await getRuleDetailHistory('rule-001', 'detail-123')

// 获取操作日志
const auditLog = await getRuleDetailsAuditLog('rule-001', {
  start_date: '2024-01-01',
  end_date: '2024-01-31'
})
```

## 🛡️ 错误处理

所有 `ruleDetails.js` 中的函数都集成了统一的错误处理机制：

- **自动重试**: 网络错误时自动重试2次
- **错误分类**: 自动识别网络错误、验证错误、权限错误等
- **用户友好提示**: 显示用户友好的错误消息
- **错误日志**: 自动记录详细的错误信息

```javascript
try {
  const result = await getRuleDetailsList('rule-001')
} catch (error) {
  // 错误已经被自动处理和显示
  // error.errorInfo 包含错误分类信息
  // error.context 包含调用上下文
}
```

## 📝 TypeScript 支持

所有API函数都有完整的TypeScript类型定义：

```typescript
import type { 
  RuleDetail,
  RuleDetailsQueryParams,
  BatchOperationRequest,
  RuleDetailsStatistics
} from '../types/api'

// 类型安全的API调用
const params: RuleDetailsQueryParams = {
  page: 1,
  page_size: 20,
  status: 'ACTIVE'
}

const result = await getRuleDetailsList('rule-001', params)
```

## ⚠️ 注意事项

1. **模块分离**: 规则明细相关函数已从 `rules.js` 迁移到 `ruleDetails.js`
2. **导入路径**: 使用 `import { functionName } from './ruleDetails'` 导入规则明细函数
3. **向后兼容**: 现有代码可以继续使用，但建议迁移到新的模块结构
4. **错误处理**: 新的API函数包含增强的错误处理，无需额外处理
5. **类型安全**: 建议使用TypeScript类型定义确保类型安全

## 🔄 迁移指南

如果你的代码中使用了 `rules.js` 中的规则明细函数，请按以下方式迁移：

```javascript
// 旧的导入方式 (已废弃)
import { getRuleDetailsList } from './rules'

// 新的导入方式 (推荐)
import { getRuleDetailsList } from './ruleDetails'
```

所有函数签名保持不变，只需要更改导入路径即可。
