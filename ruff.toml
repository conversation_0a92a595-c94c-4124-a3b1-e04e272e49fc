# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 125
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`) codes by default.
select = ["E", "F", "W", "I", "N", "UP", "B"]
ignore = ["E722", "N805", "W291", "B008", "N815", "N801", "N803", "UP015", "E712"]  # Matches the project's current use of bare except

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = [
    "UP009",   # UTF-8 encoding declaration is unnecessary
    "I001",    # Import block is un-sorted or un-formatted
    "W291",    # Trailing whitespace
    "W292",    # No newline at end of file
    "W293",    # Blank line contains whitespace
    "UP035",
    "UP038",
    "UP006",
    "UP007",
    "F401",
    "F541",
    "E501",
]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[format]
# Use "double" or "single" quotes for string literals.
quote-style = "double"

# Indent with spaces, rather than tabs.
indent-style = "space"

# Respect magic trailing commas.
skip-magic-trailing-comma = false

# Automatically detect the appropriate line ending.
line-ending = "auto"

[lint.isort]
known-first-party = ["app"]
known-third-party = ["fastapi", "loguru", "uvicorn", "pydantic"]

[lint.flake8-quotes]
docstring-quotes = "double"
inline-quotes = "double"

[lint.pydocstyle]
convention = "google" 