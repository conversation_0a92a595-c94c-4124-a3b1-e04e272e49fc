"""
统一模板生成服务
基于元数据驱动的Excel模板生成功能
"""

import time
from pathlib import Path
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleTemplate
from services.excel_template_service import ExcelTemplateService
from services.rule_detail_service import ServiceError


class UnifiedTemplateService:
    """统一模板生成服务 - 基于元数据驱动"""

    def __init__(self, session: Session, output_dir: str = "generated_templates"):
        """
        初始化服务

        Args:
            session: 数据库会话
            output_dir: 模板输出目录
        """
        self.session = session
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 初始化Excel服务
        self.excel_service = ExcelTemplateService(session, str(output_dir))

    def generate_template_by_rule_key(self, rule_key: str) -> Path:
        """
        根据规则键生成模板（基于元数据驱动）

        Args:
            rule_key: 规则模板键

        Returns:
            Path: 生成的模板文件路径

        Raises:
            ServiceError: 当生成失败时
        """
        start_time = time.time()

        try:
            # 1. 检查规则模板是否存在
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板不存在: {rule_key}",
                    error_code="TEMPLATE_NOT_FOUND",
                    details={"rule_key": rule_key},
                )

            # 2. 检查字段元数据是否存在
            field_metadata = template.get_field_metadata_list()
            if not field_metadata:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 缺少字段元数据，无法生成模板",
                    error_code="METADATA_MISSING",
                    details={"rule_key": rule_key},
                )

            # 3. 使用元数据驱动模式生成模板
            logger.info(f"使用元数据驱动模式生成模板: rule_key={rule_key}")
            output_path = self.excel_service.generate_template_by_rule_key(rule_key)

            # 4. 记录成功日志
            duration = time.time() - start_time
            logger.info(
                f"成功生成模板: rule_key={rule_key}, "
                f"fields={len(field_metadata)}, "
                f"duration={duration:.2f}s, path={output_path}"
            )

            return output_path

        except ServiceError:
            raise
        except Exception as e:
            error_msg = f"生成模板失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="TEMPLATE_GENERATION_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None


    def get_template_info(self, rule_key: str) -> dict[str, Any]:
        """
        获取模板信息（基于元数据驱动）

        Args:
            rule_key: 规则模板键

        Returns:
            Dict: 模板信息
        """
        try:
            # 检查规则模板是否存在
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板不存在: {rule_key}",
                    error_code="TEMPLATE_NOT_FOUND",
                    details={"rule_key": rule_key},
                )

            # 检查字段元数据是否存在
            field_metadata = template.get_field_metadata_list()
            if not field_metadata:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 缺少字段元数据",
                    error_code="METADATA_MISSING",
                    details={"rule_key": rule_key},
                )

            # 获取元数据信息
            metadata = self.excel_service.get_template_metadata(rule_key)
            return {
                "rule_key": rule_key,
                "mode": "metadata",
                "rule_name": metadata.rule_name,
                "total_columns": metadata.total_columns,
                "required_columns": metadata.required_columns,
                "optional_columns": metadata.optional_columns,
                "description": metadata.description,
                "columns": [
                    {
                        "field_name": col.field_name,
                        "chinese_name": col.chinese_name,
                        "data_type": col.data_type,
                        "is_required": col.is_required,
                        "description": col.description,
                    }
                    for col in metadata.columns
                ],
            }

        except ServiceError:
            raise
        except Exception as e:
            error_msg = f"获取模板信息失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GET_TEMPLATE_INFO_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def validate_template_data(self, rule_key: str, data: list[dict[str, Any]]) -> dict[str, Any]:
        """
        验证模板数据（基于元数据驱动）

        Args:
            rule_key: 规则模板键
            data: 待验证的数据列表

        Returns:
            Dict: 验证结果
        """
        try:
            # 检查规则模板是否存在
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板不存在: {rule_key}",
                    error_code="TEMPLATE_NOT_FOUND",
                    details={"rule_key": rule_key},
                )

            # 检查字段元数据是否存在
            field_metadata = template.get_field_metadata_list()
            if not field_metadata:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 缺少字段元数据",
                    error_code="METADATA_MISSING",
                    details={"rule_key": rule_key},
                )

            # 使用元数据驱动的验证
            return self.excel_service.validate_template_data(rule_key, data)

        except ServiceError as e:
            logger.error(f"验证模板数据失败: rule_key={rule_key}, error={str(e)}")
            return {
                "valid": False,
                "errors": [str(e)],
                "warnings": [],
                "total_rows": len(data) if data else 0,
                "error_rows": 1,
            }
        except Exception as e:
            error_msg = f"验证模板数据失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            return {
                "valid": False,
                "errors": [error_msg],
                "warnings": [],
                "total_rows": len(data) if data else 0,
                "error_rows": 1,
            }

    def _get_rule_template(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板"""
        return self.session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

