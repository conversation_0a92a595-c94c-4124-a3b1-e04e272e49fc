"""
Health check router for master node.
"""

from datetime import datetime

from fastapi import APIRouter

from config.validation import check_rule_registration_health
from models import ApiResponse

# Health check router (no authentication required)
health_router = APIRouter(tags=["Health Check"])


@health_router.get("/health")
async def health_check():
    """
    Health check endpoint for master node.

    Returns:
        ApiResponse: Health status response
    """
    return ApiResponse(success=True, message="Master node is healthy")


@health_router.get("/health/registration")
async def registration_health_check():
    """
    规则注册服务健康检查端点

    检查规则注册相关的配置和外部服务状态，
    用于运维监控和故障排查。

    Returns:
        ApiResponse: 规则注册健康状态响应
    """
    try:
        health_result = await check_rule_registration_health()

        # 构建响应消息
        if health_result["overall_healthy"]:
            message = "规则注册服务健康"
        else:
            issues = []
            if not health_result["configuration"]["healthy"]:
                issues.append(f"配置问题({health_result['configuration']['total_errors']}个错误)")
            if not health_result["service"]["healthy"]:
                issues.append("外部服务不可用")
            message = f"规则注册服务异常: {', '.join(issues)}"

        return ApiResponse(
            success=health_result["overall_healthy"],
            message=message,
            data={
                "overall_healthy": health_result["overall_healthy"],
                "configuration": health_result["configuration"],
                "service": health_result["service"],
                "check_time": datetime.now().isoformat(),
            },
        )

    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"健康检查失败: {e}",
            data={"error": str(e), "check_time": datetime.now().isoformat()},
        )
