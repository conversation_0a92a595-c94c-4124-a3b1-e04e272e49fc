"""
字段映射管理工具单元测试
"""

import json
import tempfile
from pathlib import Path

import pytest

from tools.field_mapping_manager import FieldMappingError, FieldMappingManager


@pytest.mark.unit
@pytest.mark.field_mapping
class TestFieldMappingManager:
    """字段映射管理工具测试类"""

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "metadata": {
                "version": "3.1.0",
                "last_updated": "2025-07-24",
                "description": "测试配置",
                "tables": ["rule_template", "rule_detail", "rule_field_metadata"],
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {
                        "chinese_name": "规则名称",
                        "data_type": "string",
                        "required": True,
                        "max_length": 500,
                        "description": "规则的显示名称",
                        "database_column": "rule_name",
                        "api_field": "rule_name",
                        "excel_column": "规则名称",
                        "validation_rules": ["required", "max_length:500"],
                    },
                    "level1": {
                        "chinese_name": "一级错误类型",
                        "data_type": "string",
                        "required": True,
                        "max_length": 100,
                        "description": "一级错误类型",
                        "database_column": "level1",
                        "api_field": "level1",
                        "excel_column": "一级错误类型",
                        "validation_rules": ["required", "max_length:100"],
                    },
                    "yb_code": {
                        "chinese_name": "药品编码",
                        "data_type": "array",
                        "required": True,
                        "description": "药品编码",
                        "database_column": "yb_code",
                        "api_field": "yb_code",
                        "excel_column": "药品编码",
                        "validation_rules": ["required", "array"],
                    },
                },
                "specific_fields": {
                    "age_threshold": {
                        "chinese_name": "年龄阈值",
                        "data_type": "integer",
                        "required": False,
                        "min_value": 0,
                        "max_value": 150,
                        "description": "年龄阈值",
                        "rule_types": ["drug_limit_adult_and_diag_exact"],
                        "database_column": "age_threshold",
                        "api_field": "age_threshold",
                        "excel_column": "年龄阈值",
                        "validation_rules": ["integer", "min:0", "max:150"],
                    }
                },
            },
            "rule_type_mappings": {
                "drug_limit_adult_and_diag_exact": {
                    "name": "药品限适应症+年龄（精确匹配诊断代码）",
                    "required_fields": ["age_threshold", "yb_code", "rule_name"],
                    "optional_fields": ["remarks"],
                }
            },
        }

    @pytest.fixture
    def temp_config_file(self, sample_config):
        """创建临时配置文件"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)
            temp_path = f.name

        yield temp_path

        # 清理临时文件
        Path(temp_path).unlink(missing_ok=True)

    class TestInitialization:
        """初始化测试组"""

        def test_init_with_valid_config_should_succeed(self, temp_config_file):
            """使用有效配置初始化应该成功"""
            manager = FieldMappingManager(temp_config_file)

            assert manager.config is not None
            assert manager.config["metadata"]["version"] == "3.1.0"
            assert len(manager._field_cache) > 0
            assert len(manager._rule_type_cache) > 0

        def test_init_with_invalid_config_path_should_fail(self):
            """使用无效配置路径初始化应该失败"""
            with pytest.raises(FieldMappingError) as exc_info:
                FieldMappingManager("nonexistent_file.json")

            assert "字段映射配置文件不存在" in str(exc_info.value)

        def test_init_with_invalid_json_should_fail(self):
            """使用无效JSON格式初始化应该失败"""
            with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
                f.write("invalid json content")
                temp_path = f.name

            try:
                with pytest.raises(FieldMappingError) as exc_info:
                    FieldMappingManager(temp_path)

                assert "字段映射配置文件格式错误" in str(exc_info.value)
            finally:
                Path(temp_path).unlink(missing_ok=True)

    class TestFieldQuery:
        """字段查询测试组"""

        def test_get_field_definition_with_existing_field_should_return_definition(self, temp_config_file):
            """获取存在的字段定义应该返回定义"""
            manager = FieldMappingManager(temp_config_file)

            field_def = manager.get_field_definition("rule_name")
            assert field_def is not None
            assert field_def["chinese_name"] == "规则名称"
            assert field_def["data_type"] == "string"
            assert field_def["required"] is True

        def test_get_field_definition_with_nonexistent_field_should_return_none(self, temp_config_file):
            """获取不存在的字段定义应该返回None"""
            manager = FieldMappingManager(temp_config_file)

            field_def = manager.get_field_definition("nonexistent_field")
            assert field_def is None

        def test_get_chinese_name_with_existing_field_should_return_chinese_name(self, temp_config_file):
            """获取存在字段的中文名称应该返回中文名称"""
            manager = FieldMappingManager(temp_config_file)

            chinese_name = manager.get_chinese_name("rule_name")
            assert chinese_name == "规则名称"

            chinese_name = manager.get_chinese_name("level1")
            assert chinese_name == "一级错误类型"

        def test_get_chinese_name_with_nonexistent_field_should_return_original_name(self, temp_config_file):
            """获取不存在字段的中文名称应该返回原字段名"""
            manager = FieldMappingManager(temp_config_file)

            chinese_name = manager.get_chinese_name("nonexistent_field")
            assert chinese_name == "nonexistent_field"

        def test_get_standard_field_name_should_return_field_name(self, temp_config_file):
            """获取标准字段名应该返回字段名"""
            manager = FieldMappingManager(temp_config_file)

            # 测试存在的字段
            standard_name = manager.get_standard_field_name("rule_name")
            assert standard_name == "rule_name"

            # 测试不存在的字段
            standard_name = manager.get_standard_field_name("unknown_field")
            assert standard_name == "unknown_field"

        def test_get_database_column_should_return_database_column(self, temp_config_file):
            """获取数据库列名应该返回数据库列名"""
            manager = FieldMappingManager(temp_config_file)

            db_column = manager.get_database_column("rule_name")
            assert db_column == "rule_name"

            db_column = manager.get_database_column("nonexistent_field")
            assert db_column == "nonexistent_field"

        def test_get_api_field_should_return_api_field(self, temp_config_file):
            """获取API字段名应该返回API字段名"""
            manager = FieldMappingManager(temp_config_file)

            api_field = manager.get_api_field("rule_name")
            assert api_field == "rule_name"

            api_field = manager.get_api_field("nonexistent_field")
            assert api_field == "nonexistent_field"

        def test_get_excel_column_should_return_excel_column(self, temp_config_file):
            """获取Excel列名应该返回Excel列名"""
            manager = FieldMappingManager(temp_config_file)

            excel_column = manager.get_excel_column("rule_name")
            assert excel_column == "规则名称"

            excel_column = manager.get_excel_column("nonexistent_field")
            assert excel_column == "nonexistent_field"

    class TestValidationRules:
        """验证规则测试组"""

        def test_get_validation_rules_with_existing_field_should_return_rules(self, temp_config_file):
            """获取存在字段的验证规则应该返回规则列表"""
            manager = FieldMappingManager(temp_config_file)

            rules = manager.get_validation_rules("rule_name")
            assert "required" in rules
            assert "max_length:500" in rules

            rules = manager.get_validation_rules("age_threshold")
            assert "integer" in rules
            assert "min:0" in rules
            assert "max:150" in rules

        def test_get_validation_rules_with_nonexistent_field_should_return_empty_list(self, temp_config_file):
            """获取不存在字段的验证规则应该返回空列表"""
            manager = FieldMappingManager(temp_config_file)

            rules = manager.get_validation_rules("nonexistent_field")
            assert rules == []

    class TestRuleTypeFields:
        """规则类型字段测试组"""

        def test_get_rule_type_fields_with_existing_rule_type_should_return_fields(self, temp_config_file):
            """获取存在规则类型的字段配置应该返回字段列表"""
            manager = FieldMappingManager(temp_config_file)

            fields = manager.get_rule_type_fields("drug_limit_adult_and_diag_exact")
            assert "age_threshold" in fields["required"]
            assert "yb_code" in fields["required"]
            assert "rule_name" in fields["required"]
            assert "remarks" in fields["optional"]

        def test_get_rule_type_fields_with_nonexistent_rule_type_should_return_empty_lists(self, temp_config_file):
            """获取不存在规则类型的字段配置应该返回空列表"""
            manager = FieldMappingManager(temp_config_file)

            fields = manager.get_rule_type_fields("nonexistent_rule_type")
            assert fields["required"] == []
            assert fields["optional"] == []

    class TestUtilityMethods:
        """工具方法测试组"""

        def test_get_all_field_names_should_return_all_field_names(self, temp_config_file):
            """获取所有字段名称应该返回所有字段名称列表"""
            manager = FieldMappingManager(temp_config_file)

            field_names = manager.get_all_field_names()
            assert "rule_name" in field_names
            assert "level1" in field_names
            assert "yb_code" in field_names
            assert "age_threshold" in field_names
            assert len(field_names) == 4

        def test_get_common_fields_should_return_common_fields(self, temp_config_file):
            """获取通用字段应该返回通用字段定义"""
            manager = FieldMappingManager(temp_config_file)

            common_fields = manager.get_common_fields()
            assert "rule_name" in common_fields
            assert "level1" in common_fields
            assert "yb_code" in common_fields
            assert "age_threshold" not in common_fields

        def test_get_specific_fields_should_return_specific_fields(self, temp_config_file):
            """获取特定字段应该返回特定字段定义"""
            manager = FieldMappingManager(temp_config_file)

            specific_fields = manager.get_specific_fields()
            assert "age_threshold" in specific_fields
            assert "rule_name" not in specific_fields

        def test_get_config_info_should_return_config_summary(self, temp_config_file):
            """获取配置信息应该返回配置摘要"""
            manager = FieldMappingManager(temp_config_file)

            info = manager.get_config_info()
            assert info["version"] == "3.1.0"
            assert info["total_fields"] == 4
            assert info["common_fields_count"] == 3
            assert info["specific_fields_count"] == 1
            assert info["rule_types_count"] == 1
            assert "config_path" in info
            # 验证新添加的字段
            assert info["config_loaded"] is True
            assert "field_definitions" in info
            assert isinstance(info["field_definitions"], dict)

    class TestConfigManagement:
        """配置管理测试组"""

        def test_reload_config_should_reload_successfully(self, temp_config_file):
            """重新加载配置应该成功"""
            manager = FieldMappingManager(temp_config_file)

            # 修改配置文件
            with open(temp_config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            config["metadata"]["version"] = "3.2.0"

            with open(temp_config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 重新加载
            manager.reload_config()
            assert manager.config["metadata"]["version"] == "3.2.0"

    class TestTypeScriptGeneration:
        """TypeScript类型生成测试组"""

        def test_generate_typescript_types_should_generate_valid_types(self, temp_config_file):
            """生成TypeScript类型应该生成有效的类型定义"""
            manager = FieldMappingManager(temp_config_file)

            ts_content = manager.generate_typescript_types()

            # 检查生成的内容
            assert "export interface CommonFields" in ts_content
            assert "export interface SpecificFields" in ts_content
            assert "export interface RuleDetail" in ts_content
            assert "export const FIELD_CHINESE_NAMES" in ts_content
            assert "export const FIELD_DATA_TYPES" in ts_content
            assert "export function getFieldChineseName" in ts_content

            # 检查字段定义
            assert "rule_name: string" in ts_content
            assert "level1: string" in ts_content
            assert "yb_code: string[]" in ts_content
            assert "age_threshold?: number" in ts_content

        def test_validate_field_usage_should_return_validation_result(self, temp_config_file):
            """验证字段使用应该返回验证结果"""
            manager = FieldMappingManager(temp_config_file)

            # 这个功能目前返回空结果
            issues = manager.validate_field_usage("/some/project/path")
            assert "missing_fields" in issues
            assert "unknown_fields" in issues
            assert "inconsistent_usage" in issues
