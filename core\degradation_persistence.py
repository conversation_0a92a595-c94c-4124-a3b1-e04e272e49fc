"""
降级状态持久化模块
支持降级状态的持久化存储和恢复，确保节点重启后能够恢复降级状态
"""

import json
import threading
import time
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Any

from core.degradation_core import DegradationEvent
from core.logging.logging_system import log as logger


@dataclass
class PersistedDegradationState:
    """持久化的降级状态"""

    current_level: str
    previous_level: str
    last_change_time: float
    active_triggers: list[str]
    is_manual_override: bool
    override_reason: str | None
    executed_actions_count: int
    degradation_duration: float
    version: str
    checksum: str
    timestamp: float

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> 'PersistedDegradationState':
        """从字典创建实例"""
        return cls(**data)


@dataclass
class PersistedSyncState:
    """持久化的同步状态"""

    last_sync_time: float
    last_successful_sync: float
    current_version: str | None
    sync_failures: int
    network_partition: bool
    cached_state: dict[str, Any] | None
    timestamp: float

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> 'PersistedSyncState':
        """从字典创建实例"""
        return cls(**data)


class DegradationPersistence:
    """降级状态持久化管理器"""

    def __init__(self, storage_dir: str = "data/degradation"):
        self.storage_dir = Path(storage_dir)
        self.degradation_state_file = self.storage_dir / "degradation_state.json"
        self.sync_state_file = self.storage_dir / "sync_state.json"
        self.events_file = self.storage_dir / "events.jsonl"
        self._lock = threading.RLock()

        # 确保存储目录存在
        self._ensure_storage_dir()

        logger.info(f"DegradationPersistence initialized with storage dir: {self.storage_dir}")

    def _ensure_storage_dir(self):
        """确保存储目录存在"""
        try:
            self.storage_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create storage directory {self.storage_dir}: {e}")
            raise

    def save_degradation_state(self, state: dict[str, Any], version: str, checksum: str) -> bool:
        """保存降级状态"""
        with self._lock:
            try:
                # 创建持久化状态对象
                persisted_state = PersistedDegradationState(
                    current_level=state.get("current_level", "normal"),
                    previous_level=state.get("previous_level", "normal"),
                    last_change_time=state.get("last_change_time", time.time()),
                    active_triggers=state.get("active_triggers", []),
                    is_manual_override=state.get("is_manual_override", False),
                    override_reason=state.get("override_reason"),
                    executed_actions_count=state.get("executed_actions_count", 0),
                    degradation_duration=state.get("degradation_duration", 0.0),
                    version=version,
                    checksum=checksum,
                    timestamp=time.time()
                )

                # 写入文件
                with open(self.degradation_state_file, 'w', encoding='utf-8') as f:
                    json.dump(persisted_state.to_dict(), f, indent=2, ensure_ascii=False)

                logger.debug(f"Degradation state saved: {version}")
                return True

            except Exception as e:
                logger.error(f"Failed to save degradation state: {e}", exc_info=True)
                return False

    def load_degradation_state(self) -> PersistedDegradationState | None:
        """加载降级状态"""
        with self._lock:
            try:
                if not self.degradation_state_file.exists():
                    logger.debug("No persisted degradation state found")
                    return None

                with open(self.degradation_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                state = PersistedDegradationState.from_dict(data)

                # 检查状态是否过期（超过24小时）
                if time.time() - state.timestamp > 86400:
                    logger.warning("Persisted degradation state is too old, ignoring")
                    return None

                logger.info(f"Degradation state loaded: {state.version}")
                return state

            except Exception as e:
                logger.error(f"Failed to load degradation state: {e}", exc_info=True)
                return None

    def save_sync_state(self, sync_state: dict[str, Any]) -> bool:
        """保存同步状态"""
        with self._lock:
            try:
                # 创建持久化同步状态对象
                persisted_sync_state = PersistedSyncState(
                    last_sync_time=sync_state.get("last_sync_time", 0.0),
                    last_successful_sync=sync_state.get("last_successful_sync", 0.0),
                    current_version=sync_state.get("current_version"),
                    sync_failures=sync_state.get("sync_failures", 0),
                    network_partition=sync_state.get("network_partition", False),
                    cached_state=sync_state.get("cached_state"),
                    timestamp=time.time()
                )

                # 写入文件
                with open(self.sync_state_file, 'w', encoding='utf-8') as f:
                    json.dump(persisted_sync_state.to_dict(), f, indent=2, ensure_ascii=False)

                logger.debug("Sync state saved")
                return True

            except Exception as e:
                logger.error(f"Failed to save sync state: {e}", exc_info=True)
                return False

    def load_sync_state(self) -> PersistedSyncState | None:
        """加载同步状态"""
        with self._lock:
            try:
                if not self.sync_state_file.exists():
                    logger.debug("No persisted sync state found")
                    return None

                with open(self.sync_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                sync_state = PersistedSyncState.from_dict(data)

                # 检查状态是否过期（超过1小时）
                if time.time() - sync_state.timestamp > 3600:
                    logger.warning("Persisted sync state is too old, ignoring")
                    return None

                logger.info("Sync state loaded")
                return sync_state

            except Exception as e:
                logger.error(f"Failed to load sync state: {e}", exc_info=True)
                return None

    def save_event(self, event: DegradationEvent) -> bool:
        """保存降级事件"""
        with self._lock:
            try:
                # 转换事件为字典
                event_data = {
                    "event_type": event.event_type.value,
                    "timestamp": event.timestamp,
                    "level": event.level.value,
                    "trigger_type": event.trigger_type.value if event.trigger_type else None,
                    "trigger_value": event.trigger_value,
                    "metadata": event.metadata,
                    "actions": [
                        {
                            "action_type": action.action_type.value,
                            "target_component": action.target_component,
                            "parameters": action.parameters
                        }
                        for action in event.actions
                    ]
                }

                # 追加到事件文件
                with open(self.events_file, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(event_data, ensure_ascii=False) + '\n')

                logger.debug(f"Event saved: {event.event_type.value}")
                return True

            except Exception as e:
                logger.error(f"Failed to save event: {e}", exc_info=True)
                return False

    def load_events(self, limit: int = 100) -> list[dict[str, Any]]:
        """加载降级事件"""
        with self._lock:
            try:
                if not self.events_file.exists():
                    logger.debug("No persisted events found")
                    return []

                events = []
                with open(self.events_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 读取最后N行
                for line in lines[-limit:]:
                    try:
                        event_data = json.loads(line.strip())
                        events.append(event_data)
                    except json.JSONDecodeError:
                        continue

                logger.debug(f"Loaded {len(events)} events")
                return events

            except Exception as e:
                logger.error(f"Failed to load events: {e}", exc_info=True)
                return []

    def cleanup_old_events(self, max_age_days: int = 30) -> bool:
        """清理旧事件"""
        with self._lock:
            try:
                if not self.events_file.exists():
                    return True

                cutoff_time = time.time() - (max_age_days * 86400)
                temp_file = self.events_file.with_suffix('.tmp')

                kept_count = 0
                with open(self.events_file, 'r', encoding='utf-8') as infile, \
                     open(temp_file, 'w', encoding='utf-8') as outfile:

                    for line in infile:
                        try:
                            event_data = json.loads(line.strip())
                            if event_data.get('timestamp', 0) > cutoff_time:
                                outfile.write(line)
                                kept_count += 1
                        except json.JSONDecodeError:
                            continue

                # 替换原文件
                temp_file.replace(self.events_file)

                logger.info(f"Event cleanup completed, kept {kept_count} events")
                return True

            except Exception as e:
                logger.error(f"Failed to cleanup old events: {e}", exc_info=True)
                return False

    def clear_all(self) -> bool:
        """清除所有持久化数据"""
        with self._lock:
            try:
                files_to_remove = [
                    self.degradation_state_file,
                    self.sync_state_file,
                    self.events_file
                ]

                for file_path in files_to_remove:
                    if file_path.exists():
                        file_path.unlink()

                logger.info("All persisted data cleared")
                return True

            except Exception as e:
                logger.error(f"Failed to clear persisted data: {e}", exc_info=True)
                return False

    def get_storage_info(self) -> dict[str, Any]:
        """获取存储信息"""
        with self._lock:
            info = {
                "storage_dir": str(self.storage_dir),
                "files": {}
            }

            files_to_check = [
                ("degradation_state", self.degradation_state_file),
                ("sync_state", self.sync_state_file),
                ("events", self.events_file)
            ]

            for name, file_path in files_to_check:
                if file_path.exists():
                    stat = file_path.stat()
                    info["files"][name] = {
                        "exists": True,
                        "size": stat.st_size,
                        "modified": stat.st_mtime
                    }
                else:
                    info["files"][name] = {"exists": False}

            return info


# 全局持久化管理器实例
_persistence_manager: DegradationPersistence | None = None


def get_degradation_persistence() -> DegradationPersistence:
    """获取全局降级持久化管理器实例"""
    global _persistence_manager

    if _persistence_manager is None:
        _persistence_manager = DegradationPersistence()

    return _persistence_manager
