from config.settings import settings
from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class ChDrugDenyUseRule(BaseRule):
    """
    中药饮片单复方均不予支付
    """

    rule_key = "ch_drug_deny_use"
    rule_name = "中药饮片单复方均不予支付"
    rule_desc = """1、排除自费患者、自费费用
2、精确匹配目标中药饮片医保代码，有则提示"""

    def __init__(
        self,
        rule_id: str,                    # 规则ID
        yb_code: str,                    # 药品编码
        rule_name: str,                  # 规则名称
        level1: str,                     # 一级错误类型
        level2: str,                     # 二级错误类型
        level3: str,                     # 三级错误类型
        error_reason: str,               # 错误原因
        degree: str,                     # 错误程度
        reference: str,                  # 质控依据或参考资料
        detail_position: str,            # 具体位置描述
        prompted_fields1: str,           # 提示字段编码
        type: str,                       # 规则类别
        pos: str,                        # 适用业务
        applicableArea: str,             # 适用地区
        default_use: str,                # 默认选用
        start_date: str,                 # 开始日期
        end_date: str,                   # 结束日期
        in_illustration: str | None = None,     # 入参说明
        remarks: str | None = None,             # 备注信息
        prompted_fields3: str | None = None,    # 提示字段类型
    ):
        super().__init__(rule_id)
        self.yb_code = yb_code
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        中药饮片单复方均不予支付
        """
        # 先判断病人医保类型，如果 为空、非字符串、自费就返回None
        if (
            not patient_data.patientMedicalInsuranceType
            or not isinstance(patient_data.patientMedicalInsuranceType, str)
            or "自费" in patient_data.patientMedicalInsuranceType
        ):
            return None

        # 使用日期集合、违规日期集合
        used_dates = set()
        illegal_dates = set()
        # 使用数量、违规数量
        used_count, illegal_count = 0, 0
        # 使用费用id列表、违规费用id列表
        used_fee_ids, illegal_fee_ids = [], []
        # 违规金额
        error_fee = 0

        for fee in patient_data.fees:
            # 判断药品编码是否违规
            if fee.ybdm != self.yb_code:
                continue

            # 判断日期是否正确，至少是秒级时间戳
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue

            fee_date = self._trans_timestamp_to_date(int(jzsj_str[:10]))
            # 下面会把自费的药品剔除掉，但是总使用量和总使用天数需要统计
            # 所以要在剔除前统计
            used_count += fee.sl
            used_dates.add(fee_date)
            used_fee_ids.append(fee.id)

            # 判断是否医保结算
            if str(fee.bzjs) in settings.FEE_SELF_PAY_CODE:  # "400"、"300"、"0"、"null" 表示自费
                continue

            # 违规数据
            illegal_dates.add(fee_date)
            illegal_count += fee.sl
            illegal_fee_ids.append(fee.id)
            error_fee += fee.je

        # 如果违规数量为0，则不违规，直接返回
        if illegal_count == 0:
            return None

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            prompted_fields2=",".join(used_fee_ids),
            illegal_item=",".join(illegal_fee_ids),
            used_count=used_count,
            illegal_count=illegal_count,
            used_day=len(used_dates),
            illegal_day=len(illegal_dates),
            error_fee=error_fee,
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
