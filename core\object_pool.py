"""
对象池管理器
减少对象创建开销，优化垃圾回收压力
"""

import queue
import threading
import time
from abc import ABC, abstractmethod
from collections.abc import Callable
from dataclasses import dataclass
from typing import Generic, TypeVar

from core.logging.logging_system import log as logger
from models.rule import RuleOutput, RuleResult

T = TypeVar("T")


class Poolable(ABC):
    """可池化对象接口"""

    @abstractmethod
    def reset(self):
        """重置对象状态，准备重用"""
        pass

    @abstractmethod
    def is_valid(self) -> bool:
        """检查对象是否有效"""
        pass


class PoolableRuleResult(RuleResult, Poolable):
    """可池化的规则结果"""

    def reset(self):
        """重置规则结果"""
        self.id = ""
        if hasattr(self.output, "reset"):
            self.output.reset()
        else:
            # 重新创建一个空的输出对象
            self.output = RuleOutput()

    def is_valid(self) -> bool:
        """检查是否有效"""
        return hasattr(self, "id") and hasattr(self, "output")


class PoolableRuleOutput(RuleOutput, Poolable):
    """可池化的规则输出"""

    def reset(self):
        """重置规则输出"""
        self.type_ = ""
        self.message = ""
        self.level1 = ""
        self.level2 = ""
        self.level3 = ""
        self.error_reason = ""
        self.degree = ""
        self.reference = ""
        self.prompted_fields3 = ""
        self.prompted_fields1 = ""
        self.prompted_fields2 = ""
        self.detail_position = ""
        self.type = ""
        self.pos = ""
        self.applicableArea = ""
        self.default_use = ""
        self.error_fee = 0.0
        self.remarks = ""
        self.in_illustration = ""
        self.used_count = 0.0
        self.illegal_count = 0.0
        self.used_day = 0
        self.illegal_day = 0
        self.illegal_item = ""

    def is_valid(self) -> bool:
        """检查是否有效"""
        return True  # RuleOutput总是有效的


@dataclass
class PoolStats:
    """对象池统计信息"""

    pool_name: str
    max_size: int
    current_size: int
    created_objects: int = 0
    borrowed_objects: int = 0
    returned_objects: int = 0
    discarded_objects: int = 0
    pool_hits: int = 0
    pool_misses: int = 0

    @property
    def hit_rate(self) -> float:
        """池命中率"""
        total_requests = self.pool_hits + self.pool_misses
        if total_requests == 0:
            return 0.0
        return self.pool_hits / total_requests * 100

    @property
    def utilization_rate(self) -> float:
        """池利用率"""
        if self.max_size == 0:
            return 0.0
        return (self.max_size - self.current_size) / self.max_size * 100


class ObjectPool(Generic[T]):
    """
    通用对象池
    支持对象重用和自动清理
    """

    def __init__(
        self,
        factory: Callable[[], T],
        max_size: int = 100,
        initial_size: int = 10,
        max_idle_time: int = 300,
        validation_interval: int = 60,
        pool_name: str = "ObjectPool",
    ):
        """
        初始化对象池

        Args:
            factory: 对象创建工厂函数
            max_size: 最大池大小
            initial_size: 初始池大小
            max_idle_time: 最大空闲时间（秒）
            validation_interval: 验证间隔（秒）
            pool_name: 池名称
        """
        self.factory = factory
        self.max_size = max_size
        self.max_idle_time = max_idle_time
        self.validation_interval = validation_interval
        self.pool_name = pool_name

        self._pool: queue.Queue[T] = queue.Queue(maxsize=max_size)
        self._lock = threading.RLock()
        self._object_timestamps: dict[id, float] = {}

        # 统计信息
        self._stats = PoolStats(pool_name=pool_name, max_size=max_size, current_size=0)

        # 预创建初始对象
        self._initialize_pool(initial_size)

        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()

        # 初始化降级适配器
        self._degradation_adapter = None
        self._adapter_initialized = False

        logger.info(f"ObjectPool '{pool_name}' initialized: max_size={max_size}, initial_size={initial_size}")

        # 延迟初始化降级适配器（在第一次使用时初始化）

    def borrow(self) -> T:
        """从池中借用对象"""
        with self._lock:
            try:
                # 尝试从池中获取对象
                obj = self._pool.get_nowait()
                self._stats.current_size -= 1
                self._stats.borrowed_objects += 1
                self._stats.pool_hits += 1

                # 移除时间戳记录
                obj_id = id(obj)
                if obj_id in self._object_timestamps:
                    del self._object_timestamps[obj_id]

                # 验证对象有效性
                if isinstance(obj, Poolable) and not obj.is_valid():
                    # 对象无效，创建新对象
                    self._stats.discarded_objects += 1
                    return self._create_new_object()

                return obj

            except queue.Empty:
                # 池为空，创建新对象
                self._stats.pool_misses += 1
                return self._create_new_object()

    def return_object(self, obj: T) -> bool:
        """归还对象到池"""
        if obj is None:
            return False

        with self._lock:
            # 检查池是否已满
            if self._stats.current_size >= self.max_size:
                self._stats.discarded_objects += 1
                return False

            # 重置对象状态
            if isinstance(obj, Poolable):
                try:
                    obj.reset()
                    if not obj.is_valid():
                        self._stats.discarded_objects += 1
                        return False
                except Exception as e:
                    logger.warning(f"Failed to reset poolable object: {e}")
                    self._stats.discarded_objects += 1
                    return False

            try:
                # 归还到池
                self._pool.put_nowait(obj)
                self._stats.current_size += 1
                self._stats.returned_objects += 1

                # 记录归还时间
                self._object_timestamps[id(obj)] = time.time()

                return True

            except queue.Full:
                # 池已满（理论上不应该发生）
                self._stats.discarded_objects += 1
                return False

    def _create_new_object(self) -> T:
        """创建新对象"""
        try:
            obj = self.factory()
            self._stats.created_objects += 1
            return obj
        except Exception as e:
            logger.error(f"Failed to create object in pool '{self.pool_name}': {e}")
            raise

    def _initialize_pool(self, size: int):
        """初始化池"""
        for _ in range(min(size, self.max_size)):
            try:
                obj = self._create_new_object()
                if self.return_object(obj):
                    continue
                else:
                    break
            except Exception as e:
                logger.error(f"Failed to initialize pool '{self.pool_name}': {e}")
                break

    def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                time.sleep(self.validation_interval)
                self._cleanup_idle_objects()
            except Exception as e:
                logger.error(f"Error in pool cleanup for '{self.pool_name}': {e}")

    def _cleanup_idle_objects(self):
        """清理空闲对象"""
        with self._lock:
            current_time = time.time()
            expired_objects = []

            # 找出过期对象
            for obj_id, timestamp in self._object_timestamps.items():
                if current_time - timestamp > self.max_idle_time:
                    expired_objects.append(obj_id)

            # 从池中移除过期对象
            if expired_objects:
                temp_objects = []
                removed_count = 0

                # 取出所有对象，过滤掉过期的
                while not self._pool.empty():
                    try:
                        obj = self._pool.get_nowait()
                        obj_id = id(obj)

                        if obj_id in expired_objects:
                            # 过期对象，不放回池中
                            if obj_id in self._object_timestamps:
                                del self._object_timestamps[obj_id]
                            removed_count += 1
                            self._stats.discarded_objects += 1
                        else:
                            # 有效对象，暂存
                            temp_objects.append(obj)
                    except queue.Empty:
                        break

                # 将有效对象放回池中
                for obj in temp_objects:
                    try:
                        self._pool.put_nowait(obj)
                    except queue.Full:
                        break

                self._stats.current_size = self._pool.qsize()

                if removed_count > 0:
                    logger.debug(f"Pool '{self.pool_name}' cleaned up {removed_count} idle objects")

    def get_stats(self) -> PoolStats:
        """获取池统计信息"""
        with self._lock:
            return PoolStats(
                pool_name=self._stats.pool_name,
                max_size=self._stats.max_size,
                current_size=self._stats.current_size,
                created_objects=self._stats.created_objects,
                borrowed_objects=self._stats.borrowed_objects,
                returned_objects=self._stats.returned_objects,
                discarded_objects=self._stats.discarded_objects,
                pool_hits=self._stats.pool_hits,
                pool_misses=self._stats.pool_misses,
            )

    def clear(self):
        """清空池"""
        with self._lock:
            while not self._pool.empty():
                try:
                    self._pool.get_nowait()
                except queue.Empty:
                    break

            self._object_timestamps.clear()
            self._stats.current_size = 0
            logger.info(f"Pool '{self.pool_name}' cleared")

    def _init_degradation_adapter(self):
        """初始化降级适配器"""
        if self._adapter_initialized:
            return

        try:
            from core.degradation_adapters import ObjectPoolAdapter, get_adapter_manager

            # 创建适配器
            self._degradation_adapter = ObjectPoolAdapter(self)

            # 注册到适配器管理器
            adapter_manager = get_adapter_manager()
            adapter_manager.register_adapter(self._degradation_adapter)

            self._adapter_initialized = True
            logger.info(f"ObjectPool '{self.pool_name}' degradation adapter initialized and registered")

        except ImportError:
            logger.debug("Degradation adapters not available, skipping initialization")
            self._adapter_initialized = True  # 标记为已尝试初始化
        except Exception as e:
            logger.error(f"Failed to initialize pool '{self.pool_name}': {e}")
            # 确保适配器为None，避免后续访问错误
            self._degradation_adapter = None
            self._adapter_initialized = True  # 标记为已尝试初始化

    def get_degradation_adapter(self):
        """获取降级适配器"""
        return self._degradation_adapter

    def get_object(self):
        """获取对象（考虑降级状态）"""
        # 延迟初始化降级适配器
        if not self._adapter_initialized:
            self._init_degradation_adapter()

        if self._degradation_adapter and hasattr(self._degradation_adapter, "get_object"):
            return self._degradation_adapter.get_object()
        else:
            return self.borrow()

    def put_object(self, obj):
        """归还对象（考虑降级状态）"""
        # 延迟初始化降级适配器
        if not self._adapter_initialized:
            self._init_degradation_adapter()

        if self._degradation_adapter and hasattr(self._degradation_adapter, "return_object"):
            self._degradation_adapter.return_object(obj)
        else:
            # 直接调用内部的归还逻辑，避免通过适配器
            self._return_object_internal(obj)

    def _return_object_internal(self, obj):
        """内部归还对象方法，不经过降级适配器"""
        return self.return_object(obj)


class ObjectPoolManager:
    """对象池管理器"""

    def __init__(self):
        """初始化对象池管理器"""
        self._pools: dict[str, ObjectPool] = {}
        self._lock = threading.RLock()

        # 创建常用对象池
        self._create_default_pools()

        logger.info("ObjectPoolManager initialized")

    def initialize_degradation_adapters(self):
        """初始化所有对象池的降级适配器"""
        for pool in self._pools.values():
            if hasattr(pool, "_init_degradation_adapter"):
                pool._init_degradation_adapter()
        logger.info("All object pool degradation adapters initialized")

    def _create_default_pools(self):
        """创建默认对象池"""
        # 规则结果池
        self.rule_result_pool = ObjectPool(
            factory=lambda: PoolableRuleResult(id="", output=PoolableRuleOutput()),
            max_size=1000,
            initial_size=50,
            pool_name="RuleResult",
        )
        self._pools["rule_result"] = self.rule_result_pool

        # 规则输出池
        self.rule_output_pool = ObjectPool(
            factory=lambda: PoolableRuleOutput(), max_size=1000, initial_size=50, pool_name="RuleOutput"
        )
        self._pools["rule_output"] = self.rule_output_pool

    def get_pool(self, pool_name: str) -> ObjectPool | None:
        """获取指定名称的对象池"""
        with self._lock:
            return self._pools.get(pool_name)

    def create_pool(
        self, pool_name: str, factory: Callable[[], T], max_size: int = 100, initial_size: int = 10
    ) -> ObjectPool[T]:
        """创建新的对象池"""
        with self._lock:
            if pool_name in self._pools:
                raise ValueError(f"Pool '{pool_name}' already exists")

            pool = ObjectPool(factory=factory, max_size=max_size, initial_size=initial_size, pool_name=pool_name)

            self._pools[pool_name] = pool
            return pool

    def get_all_stats(self) -> dict[str, PoolStats]:
        """获取所有池的统计信息"""
        with self._lock:
            return {name: pool.get_stats() for name, pool in self._pools.items()}

    def clear_all_pools(self):
        """清空所有池"""
        with self._lock:
            for pool in self._pools.values():
                pool.clear()
            logger.info("All object pools cleared")


# 全局对象池管理器实例
object_pool_manager = ObjectPoolManager()


def get_object_pool_manager():
    """获取全局对象池管理器实例"""
    return object_pool_manager
