"""
增强数据模型单元测试
测试新增的模型方法和功能
"""


from models.database import (
    FieldTypeEnum,
    RuleDetail,
    RuleDetailStatusEnum,
    RuleFieldMetadata,
    RuleTemplate,
    RuleTemplateStatusEnum,
)


class TestRuleDetailEnhanced:
    """测试RuleDetail模型的增强功能"""

    def test_from_dict_creation(self):
        """测试从字典创建RuleDetail对象"""
        data = {
            "rule_id": "test_001",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "level1": "一级错误",
            "level2": "二级错误",
            "level3": "三级错误",
            "error_reason": "测试错误原因",
            "degree": "轻微",
            "reference": "测试参考",
            "detail_position": "测试位置",
            "prompted_fields1": "test_field",
            "type": "测试类型",
            "pos": "测试业务",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "status": "ACTIVE"
        }

        rule_detail = RuleDetail.from_dict(data)

        assert rule_detail.rule_id == "test_001"
        assert rule_detail.rule_key == "test_rule"
        assert rule_detail.rule_name == "测试规则"
        assert rule_detail.status == RuleDetailStatusEnum.ACTIVE

    def test_extended_fields_operations(self):
        """测试扩展字段操作"""
        rule_detail = RuleDetail(
            rule_id="test_001",
            rule_key="test_rule",
            rule_name="测试规则",
            level1="一级错误",
            level2="二级错误",
            level3="三级错误",
            error_reason="测试错误原因",
            degree="轻微",
            reference="测试参考",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="测试类型",
            pos="测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )

        # 测试设置扩展字段
        rule_detail.set_extended_field("age_threshold", 18)
        rule_detail.set_extended_field("limit_days", 30)

        # 测试获取扩展字段
        assert rule_detail.get_extended_field("age_threshold") == 18
        assert rule_detail.get_extended_field("limit_days") == 30
        assert rule_detail.get_extended_field("non_existent", "default") == "default"

        # 测试获取所有扩展字段
        all_fields = rule_detail.get_all_extended_fields()
        assert all_fields["age_threshold"] == 18
        assert all_fields["limit_days"] == 30

        # 测试批量更新扩展字段
        rule_detail.update_extended_fields({
            "age_threshold": 21,
            "new_field": "new_value"
        })

        assert rule_detail.get_extended_field("age_threshold") == 21
        assert rule_detail.get_extended_field("new_field") == "new_value"

    def test_to_api_response(self):
        """测试API响应格式转换"""
        rule_detail = RuleDetail(
            rule_id="test_001",
            rule_key="test_rule",
            rule_name="测试规则",
            level1="一级错误",
            level2="二级错误",
            level3="三级错误",
            error_reason="测试错误原因",
            degree="轻微",
            reference="测试参考",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="测试类型",
            pos="测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )

        # 添加扩展字段
        rule_detail.set_extended_field("age_threshold", 18)

        response = rule_detail.to_api_response()

        # 验证基础字段
        assert response["rule_id"] == "test_001"
        assert response["rule_name"] == "测试规则"

        # 验证扩展字段被合并
        assert response["age_threshold"] == 18

    def test_merge_from_dict(self):
        """测试从字典合并数据"""
        rule_detail = RuleDetail(
            rule_id="test_001",
            rule_key="test_rule",
            rule_name="原始规则",
            level1="一级错误",
            level2="二级错误",
            level3="三级错误",
            error_reason="测试错误原因",
            degree="轻微",
            reference="测试参考",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="测试类型",
            pos="测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )

        # 合并数据
        update_data = {
            "rule_name": "更新后的规则",
            "degree": "严重",
            "age_threshold": 21,
            "new_field": "新字段值"
        }

        rule_detail.merge_from_dict(update_data)

        # 验证固定字段更新
        assert rule_detail.rule_name == "更新后的规则"
        assert rule_detail.degree == "严重"

        # 验证扩展字段更新
        assert rule_detail.get_extended_field("age_threshold") == 21
        assert rule_detail.get_extended_field("new_field") == "新字段值"


class TestRuleTemplateEnhanced:
    """测试RuleTemplate模型的增强功能"""

    def test_from_dict_creation(self):
        """测试从字典创建RuleTemplate对象"""
        data = {
            "rule_key": "test_rule",
            "rule_type": "测试类型",
            "name": "测试规则模板",
            "description": "测试描述",
            "module_path": "test.module",
            "status": "NEW"
        }

        template = RuleTemplate.from_dict(data)

        assert template.rule_key == "test_rule"
        assert template.rule_type == "测试类型"
        assert template.name == "测试规则模板"
        assert template.status == RuleTemplateStatusEnum.NEW

    def test_validate_template(self):
        """测试模板验证"""
        # 测试有效模板
        template = RuleTemplate(
            rule_key="test_rule",
            rule_type="测试类型",
            name="测试规则模板"
        )

        validation = template.validate_template()
        assert validation["valid"] is True
        assert len(validation["errors"]) == 0

        # 测试无效模板
        invalid_template = RuleTemplate()
        validation = invalid_template.validate_template()
        assert validation["valid"] is False
        assert "rule_key不能为空" in validation["errors"]
        assert "name不能为空" in validation["errors"]
        assert "rule_type不能为空" in validation["errors"]


class TestRuleFieldMetadataEnhanced:
    """测试RuleFieldMetadata模型的增强功能"""

    def test_from_dict_creation(self):
        """测试从字典创建RuleFieldMetadata对象"""
        data = {
            "rule_key": "test_rule",
            "field_name": "test_field",
            "field_type": "string",
            "is_required": True,
            "display_name": "测试字段",
            "description": "测试字段描述",
            "validation_rule": '["required", "max_length:100"]',
            "excel_column_order": 1
        }

        metadata = RuleFieldMetadata.from_dict(data)

        assert metadata.rule_key == "test_rule"
        assert metadata.field_name == "test_field"
        assert metadata.field_type == FieldTypeEnum.STRING
        assert metadata.is_required is True

    def test_get_validation_rules(self):
        """测试获取验证规则"""
        metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="test_field",
            field_type=FieldTypeEnum.STRING,
            validation_rule='["required", "max_length:100"]'
        )

        rules = metadata.get_validation_rules()
        assert "required" in rules
        assert "max_length:100" in rules

        # 测试无效JSON
        metadata.validation_rule = "invalid_json"
        rules = metadata.get_validation_rules()
        assert rules == []

    def test_validate_field_value(self):
        """测试字段值验证"""
        metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="test_field",
            field_type=FieldTypeEnum.STRING,
            is_required=True,
            display_name="测试字段",
            validation_rule='["required", "max_length:10"]'
        )

        # 测试有效值
        result = metadata.validate_field_value("test")
        assert result["valid"] is True

        # 测试空值（必填字段）
        result = metadata.validate_field_value("")
        assert result["valid"] is False
        assert "测试字段不能为空" in result["errors"]

        # 测试超长值
        result = metadata.validate_field_value("this_is_too_long")
        assert result["valid"] is False
        assert "测试字段长度不能超过10个字符" in result["errors"]

    def test_get_default_value_parsed(self):
        """测试解析默认值"""
        # 测试字符串类型
        metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="test_field",
            field_type=FieldTypeEnum.STRING,
            default_value="test_default"
        )
        assert metadata.get_default_value_parsed() == "test_default"

        # 测试整数类型
        metadata.field_type = FieldTypeEnum.INTEGER
        metadata.default_value = "42"
        assert metadata.get_default_value_parsed() == 42

        # 测试布尔类型
        metadata.field_type = FieldTypeEnum.BOOLEAN
        metadata.default_value = "true"
        assert metadata.get_default_value_parsed() is True

        # 测试数组类型
        metadata.field_type = FieldTypeEnum.ARRAY
        metadata.default_value = '["item1", "item2"]'
        assert metadata.get_default_value_parsed() == ["item1", "item2"]
