"""
数据差异分析器
负责比较现有数据库数据和新上传数据，生成DELETE和UPSERT操作列表
"""

import time
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleDetail, RuleTemplate


class DifferenceAnalysisError(Exception):
    """数据差异分析相关异常"""

    def __init__(self, message: str, rule_key: str | None = None, details: dict | None = None):
        super().__init__(message)
        self.rule_key = rule_key
        self.details = details or {}


class DifferenceAnalyzer:
    """
    数据差异分析器

    负责比较现有数据库数据和新上传数据，生成DELETE和UPSERT操作列表。
    使用高效的集合运算优化性能，支持大数据量处理。
    """

    def __init__(self):
        """初始化差异分析器"""
        self._analysis_stats = {
            "total_analyses": 0,
            "total_existing_rules": 0,
            "total_new_rules": 0,
            "total_delete_operations": 0,
            "total_upsert_operations": 0,
            "average_analysis_time": 0.0,
        }

        logger.info("DifferenceAnalyzer初始化完成")

    def analyze_data_diff(self, rule_key: str, new_data: list[dict[str, Any]], session: Session) -> dict[str, Any]:
        """
        分析数据差异，生成DELETE和UPSERT操作列表

        Args:
            rule_key: 规则键值
            new_data: 新上传的数据列表（已映射为注册格式）
            session: 数据库会话

        Returns:
            Dict: 包含差异分析结果的字典
            {
                "delete_operations": List[Dict],  # 需要删除的规则列表
                "upsert_operations": List[Dict],  # 需要新增/更新的规则列表
                "stats": Dict  # 统计信息
            }

        Raises:
            DifferenceAnalysisError: 分析过程中发生错误
        """
        start_time = time.time()
        self._analysis_stats["total_analyses"] += 1

        try:
            logger.info(f"开始分析规则 '{rule_key}' 的数据差异，新数据量: {len(new_data)}")

            # 1. 获取现有数据中的rule_id集合
            existing_rule_ids = self._get_existing_rule_ids(rule_key, session)

            # 2. 从新数据中提取rule_id集合
            new_rule_ids = self._extract_new_rule_ids(new_data)

            # 3. 使用集合运算计算差异
            to_delete_ids = existing_rule_ids - new_rule_ids
            to_upsert_ids = new_rule_ids

            # 4. 构建操作列表
            delete_operations = self._build_delete_operations(to_delete_ids, rule_key)
            upsert_operations = self._build_upsert_operations(to_upsert_ids, new_data)

            # 5. 更新统计信息
            analysis_time = time.time() - start_time
            self._update_analysis_stats(
                len(existing_rule_ids), len(new_rule_ids), len(delete_operations), len(upsert_operations), analysis_time
            )

            result = {
                "delete_operations": delete_operations,
                "upsert_operations": upsert_operations,
                "stats": {
                    "existing_rules_count": len(existing_rule_ids),
                    "new_rules_count": len(new_rule_ids),
                    "delete_count": len(delete_operations),
                    "upsert_count": len(upsert_operations),
                    "analysis_time": analysis_time,
                    "rule_key": rule_key,
                },
            }

            logger.info(
                f"规则 '{rule_key}' 差异分析完成 - "
                f"现有: {len(existing_rule_ids)}, 新增: {len(new_rule_ids)}, "
                f"删除: {len(delete_operations)}, 更新: {len(upsert_operations)}, "
                f"耗时: {analysis_time:.3f}s"
            )

            return result

        except Exception as e:
            analysis_time = time.time() - start_time
            error_message = f"规则 '{rule_key}' 差异分析失败: {str(e)}"
            logger.error(error_message, exc_info=True)

            raise DifferenceAnalysisError(
                error_message,
                rule_key=rule_key,
                details={"new_data_count": len(new_data) if new_data else 0, "analysis_time": analysis_time},
            ) from e

    def _get_existing_rule_ids(self, rule_key: str, session: Session) -> set[str]:
        """
        从数据库获取现有的rule_id集合

        Args:
            rule_key: 规则键值
            session: 数据库会话

        Returns:
            Set[str]: 现有的rule_id集合
        """
        try:
            # 查询规则模板
            rule_template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

            if not rule_template:
                logger.warning(f"规则 '{rule_key}' 不存在，返回空的现有rule_id集合")
                return set()

            # 查询活跃的规则明细
            active_details = session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key).all()

            if not active_details:
                logger.info(f"规则 '{rule_key}' 没有活跃的规则明细")
                return set()

            # 提取rule_id
            rule_ids = set()

            for detail in active_details:
                try:
                    # 直接从RuleDetail对象获取rule_id
                    if detail.rule_id:
                        rule_ids.add(str(detail.rule_id))

                except Exception as e:
                    logger.error(f"处理规则明细 {detail.id} 时出错: {e}")
                    continue

            logger.debug(f"从规则 '{rule_key}' 提取到 {len(rule_ids)} 个现有rule_id")
            return rule_ids

        except Exception as e:
            logger.error(f"获取规则 '{rule_key}' 现有rule_id时发生错误: {e}")
            raise

    def _extract_new_rule_ids(self, new_data: list[dict[str, Any]]) -> set[str]:
        """
        从新数据中提取rule_id集合

        Args:
            new_data: 新上传的数据列表（已映射为注册格式）

        Returns:
            Set[str]: 新数据的rule_id集合
        """
        if not new_data:
            return set()

        rule_ids = set()

        for item in new_data:
            if not isinstance(item, dict):
                logger.warning(f"新数据包含非字典项: {type(item)}")
                continue

            rule_id = item.get("id")  # 注册格式中使用"id"字段
            if rule_id:
                rule_ids.add(str(rule_id))
            else:
                logger.warning(f"新数据项缺少id字段: {item}")

        logger.debug(f"从新数据中提取到 {len(rule_ids)} 个rule_id")
        return rule_ids

    def _build_delete_operations(self, rule_ids: set[str], rule_key: str) -> list[dict[str, Any]]:
        """
        构建DELETE操作列表

        Args:
            rule_ids: 需要删除的rule_id集合
            rule_key: 规则键值

        Returns:
            List[Dict]: DELETE操作列表
        """
        if not rule_ids:
            return []

        delete_operations = []

        for rule_id in rule_ids:
            delete_operation = {
                "id": rule_id,
                "name": f"删除规则_{rule_id}",  # 删除操作的名称
                "outputs": [],  # DELETE操作不需要outputs
                "script": "print('this is python script')",  # 固定脚本内容
                "createTime": int(time.time() * 1000),  # 当前时间戳（毫秒）
                "operate": "DELETE",
            }
            delete_operations.append(delete_operation)

        logger.debug(f"构建了 {len(delete_operations)} 个DELETE操作")
        return delete_operations

    def _build_upsert_operations(self, rule_ids: set[str], new_data: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        构建UPSERT操作列表

        Args:
            rule_ids: 需要UPSERT的rule_id集合
            new_data: 新上传的数据列表（已映射为注册格式）

        Returns:
            List[Dict]: UPSERT操作列表
        """
        if not rule_ids or not new_data:
            return []

        # 创建rule_id到数据的映射
        data_by_id = {}
        for item in new_data:
            rule_id = item.get("id")
            if rule_id:
                data_by_id[str(rule_id)] = item

        upsert_operations = []

        for rule_id in rule_ids:
            if rule_id in data_by_id:
                # 复制数据并确保操作类型为UPSERT
                operation = data_by_id[rule_id].copy()
                operation["operate"] = "UPSERT"
                upsert_operations.append(operation)
            else:
                logger.warning(f"rule_id {rule_id} 在新数据中未找到对应项")

        logger.debug(f"构建了 {len(upsert_operations)} 个UPSERT操作")
        return upsert_operations

    def _update_analysis_stats(
        self, existing_count: int, new_count: int, delete_count: int, upsert_count: int, analysis_time: float
    ):
        """更新分析统计信息"""
        self._analysis_stats["total_existing_rules"] += existing_count
        self._analysis_stats["total_new_rules"] += new_count
        self._analysis_stats["total_delete_operations"] += delete_count
        self._analysis_stats["total_upsert_operations"] += upsert_count

        # 更新平均分析时间
        total_analyses = self._analysis_stats["total_analyses"]
        current_avg = self._analysis_stats["average_analysis_time"]

        if total_analyses == 1:
            self._analysis_stats["average_analysis_time"] = analysis_time
        else:
            self._analysis_stats["average_analysis_time"] = (
                current_avg * (total_analyses - 1) + analysis_time
            ) / total_analyses

    def get_analysis_stats(self) -> dict[str, Any]:
        """
        获取分析统计信息

        Returns:
            Dict: 统计信息
        """
        stats = self._analysis_stats.copy()

        # 添加计算字段
        if stats["total_analyses"] > 0:
            stats["avg_existing_rules_per_analysis"] = stats["total_existing_rules"] / stats["total_analyses"]
            stats["avg_new_rules_per_analysis"] = stats["total_new_rules"] / stats["total_analyses"]
            stats["avg_operations_per_analysis"] = (
                stats["total_delete_operations"] + stats["total_upsert_operations"]
            ) / stats["total_analyses"]
        else:
            stats["avg_existing_rules_per_analysis"] = 0
            stats["avg_new_rules_per_analysis"] = 0
            stats["avg_operations_per_analysis"] = 0

        return stats

    def analyze_batch_diff(
        self, batch_data: list[tuple[str, list[dict[str, Any]]]], session: Session
    ) -> dict[str, Any]:
        """
        批量分析多个规则的数据差异

        Args:
            batch_data: 批量数据列表，每个元素为(rule_key, new_data)元组
            session: 数据库会话

        Returns:
            Dict: 批量分析结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始批量差异分析，规则数量: {len(batch_data)}")

            batch_results = {}
            total_delete_ops = 0
            total_upsert_ops = 0

            for rule_key, new_data in batch_data:
                try:
                    result = self.analyze_data_diff(rule_key, new_data, session)
                    batch_results[rule_key] = result

                    total_delete_ops += len(result["delete_operations"])
                    total_upsert_ops += len(result["upsert_operations"])

                except Exception as e:
                    logger.error(f"批量分析中规则 '{rule_key}' 失败: {e}")
                    batch_results[rule_key] = {
                        "error": str(e),
                        "delete_operations": [],
                        "upsert_operations": [],
                        "stats": {},
                    }

            batch_time = time.time() - start_time

            logger.info(
                f"批量差异分析完成 - "
                f"规则数: {len(batch_data)}, "
                f"总删除操作: {total_delete_ops}, "
                f"总更新操作: {total_upsert_ops}, "
                f"耗时: {batch_time:.3f}s"
            )

            return {
                "results": batch_results,
                "summary": {
                    "total_rules": len(batch_data),
                    "successful_analyses": len([r for r in batch_results.values() if "error" not in r]),
                    "failed_analyses": len([r for r in batch_results.values() if "error" in r]),
                    "total_delete_operations": total_delete_ops,
                    "total_upsert_operations": total_upsert_ops,
                    "batch_analysis_time": batch_time,
                },
            }

        except Exception as e:
            batch_time = time.time() - start_time
            error_message = f"批量差异分析失败: {str(e)}"
            logger.error(error_message, exc_info=True)

            raise DifferenceAnalysisError(
                error_message, details={"batch_size": len(batch_data), "batch_analysis_time": batch_time}
            ) from e
