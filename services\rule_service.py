import asyncio
import signal
import time
from concurrent.futures import Process<PERSON>oolExecutor
from typing import Any

from config.settings import settings
from core.dynamic_process_pool import DynamicProcessPool
from core.intelligent_cache import intelligent_cache
from core.logging.logging_system import log as logger
from core.object_pool import object_pool_manager
from core.performance_monitor import performance_monitor
from core.rule_cache import RULE_CACHE
from models.optimized_patient import convert_to_optimized
from models.patient import PatientData
from models.rule import RuleResult
from rules.base_rules.base import BaseRule
from utils.rule_grouping import rule_grouping_strategy


def ignore_sigint():
    """
    Initializer for ProcessPoolExecutor workers.
    Makes child processes ignore the SIGINT signal (sent by Ctrl+C).
    This allows the main process to handle graceful shutdown.
    """
    signal.signal(signal.SIGINT, signal.SIG_IGN)


def _execute_rule_in_process(rule: BaseRule, patient_data: PatientData) -> RuleResult | None:
    """
    一个独立的、可被序列化的顶层函数，用于在子进程中执行单个规则的校验。
    注意：为了能在多进程间传递，参数和返回值必须是可序列化的（picklable）。

    Args:
        rule (BaseRule): 要执行的规则实例。
        patient_data (PatientData): 患者数据模型。

    Returns:
        RuleResult | None: 如果规则被触发，则返回结果；否则返回 None。
    """
    try:
        # BaseRule 的 validate 方法现在是同步的
        return rule.validate(patient_data)
    except Exception as e:
        logger.error(f"Error executing rule '{rule.rule_id}' in a subprocess: {e}", exc_info=True)
        # 如果需要，可以将错误封装成 RuleResult 返回
        return None


def _execute_rule_group_in_process(rule_group: list[BaseRule], patient_data: PatientData) -> list[RuleResult]:
    """
    在子进程中批量执行一组规则的校验。

    Args:
        rule_group: 规则实例列表
        patient_data: 患者数据模型

    Returns:
        规则结果列表（不包含None）
    """
    results = []
    start_time = time.time()

    try:
        for rule in rule_group:
            try:
                result = rule.validate(patient_data)
                if result is not None:
                    results.append(result)
            except Exception as e:
                logger.error(f"Error executing rule '{rule.rule_id}' in group: {e}")

        # 记录执行时间用于性能分析
        execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
        logger.debug(
            f"Executed {len(rule_group)} rules in group, "
            f"found {len(results)} violations, time: {execution_time:.2f}ms",
        )

        return results

    except Exception as e:
        logger.error(f"Error executing rule group: {e}", exc_info=True)
        return results


def _execute_rule_group_optimized(rule_group: list[BaseRule], patient_data: PatientData) -> list[RuleResult]:
    """
    优化版本的规则组执行函数
    使用缓存、对象池和优化的患者数据结构

    Args:
        rule_group: 规则实例列表
        patient_data: 患者数据模型

    Returns:
        规则结果列表（不包含None）
    """
    results = []
    start_time = time.time()

    try:
        # 转换为优化的患者数据结构
        optimized_patient = convert_to_optimized(patient_data)
        patient_hash = str(hash(optimized_patient))

        for rule in rule_group:
            try:
                # 检查缓存
                cached_result = intelligent_cache.get_rule_result(patient_hash, rule.rule_id)
                if cached_result is not None:
                    results.append(cached_result)
                    continue

                # 执行规则
                result = rule.validate(patient_data)
                if result is not None:
                    results.append(result)
                    # 缓存结果
                    intelligent_cache.cache_rule_result(patient_hash, rule.rule_id, result)

                # 记录执行时间到分组策略
                rule_execution_time = (time.time() - start_time) * 1000
                rule_grouping_strategy.record_execution_time(rule.rule_id, rule_execution_time)

            except Exception as e:
                logger.error(f"Error executing rule '{rule.rule_id}' in optimized group: {e}")

        # 记录执行时间用于性能分析
        execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
        logger.debug(
            f"Executed {len(rule_group)} rules in optimized group, "
            f"found {len(results)} violations, time: {execution_time:.2f}ms",
        )

        return results

    except Exception as e:
        logger.error(f"Error executing optimized rule group: {e}", exc_info=True)
        return results


class RuleService:
    """
    优化的规则处理服务。
    使用动态进程池和智能分组策略，以提升CPU密集型任务的性能。
    """

    def __init__(self, use_dynamic_pool: bool = True, use_memory_optimization: bool = True):
        """
        初始化规则服务。

        Args:
            use_dynamic_pool: 是否使用动态进程池
            use_memory_optimization: 是否使用内存优化（缓存、对象池等）
        """
        self.use_dynamic_pool = use_dynamic_pool
        self.use_memory_optimization = use_memory_optimization

        if use_dynamic_pool:
            # 使用动态进程池
            self.dynamic_pool = DynamicProcessPool(
                min_workers=max(2, settings.WORKER_COUNT // 2),
                max_workers=settings.WORKER_COUNT,
                max_tasks_per_child=settings.RULE_SERVICE_MAX_TASKS_PER_WORKER,
            )
            self.executor = None
            logger.info(
                f"RuleService initialized with DynamicProcessPool "
                f"(workers: {self.dynamic_pool.min_workers}-{self.dynamic_pool.max_workers})",
            )
        else:
            # 使用传统进程池
            self.dynamic_pool = None
            self.executor = ProcessPoolExecutor(
                max_workers=settings.WORKER_COUNT,
                initializer=ignore_sigint,
            )
            logger.info(f"RuleService initialized with ProcessPoolExecutor (max_workers={settings.WORKER_COUNT})")

        # 性能统计
        self.total_validations = 0
        self.total_violations = 0
        self.total_time = 0.0

    async def start(self):
        """启动规则服务"""
        if self.use_dynamic_pool and self.dynamic_pool:
            await self.dynamic_pool.start()
            logger.info("RuleService started with dynamic process pool")
        else:
            logger.info("RuleService started with static process pool")

    async def stop(self):
        """停止规则服务"""
        if self.use_dynamic_pool and self.dynamic_pool:
            await self.dynamic_pool.stop()
        elif self.executor:
            self.executor.shutdown(wait=True)
        logger.info("RuleService stopped")

    async def validate_rules(self, patient_data: PatientData, ids: list[str]) -> list[RuleResult]:
        """
        使用优化的进程池并行校验指定的规则列表。

        Args:
            patient_data: 患者数据。
            ids: 要验证的规则ID列表。

        Returns:
            list[RuleResult]: 违规规则的结果列表。
        """
        start_time = time.time()

        # 从缓存中获取规则实例
        rules_to_validate: list[BaseRule] = []
        for rule_id in ids:
            rule_instance = RULE_CACHE.get(rule_id)
            if rule_instance and isinstance(rule_instance, BaseRule):
                rules_to_validate.append(rule_instance)

        if not rules_to_validate:
            logger.warning("No valid rules found to execute for the given IDs.")
            return []

        # 使用智能分组策略
        rule_groups = rule_grouping_strategy.group_rules_by_complexity([rule.rule_id for rule in rules_to_validate])

        # 根据分组重新组织规则实例
        grouped_rules = []
        for group in rule_groups:
            group_rules = [rule for rule in rules_to_validate if rule.rule_id in group.rule_ids]
            if group_rules:
                grouped_rules.append(group_rules)

        # 选择执行函数
        if self.use_memory_optimization:
            execute_func = _execute_rule_group_optimized
        else:
            execute_func = _execute_rule_group_in_process

        # 并行执行各组规则
        if self.use_dynamic_pool and self.dynamic_pool:
            # 使用动态进程池
            tasks = []
            for group_rules in grouped_rules:
                task = self.dynamic_pool.submit(execute_func, group_rules, patient_data)
                tasks.append(task)

            # 等待所有任务完成
            group_results = await asyncio.gather(*tasks)
        else:
            # 使用传统进程池
            loop = asyncio.get_running_loop()
            tasks = []
            for group_rules in grouped_rules:
                task = loop.run_in_executor(
                    self.executor,
                    execute_func,
                    group_rules,
                    patient_data,
                )
                tasks.append(task)

            group_results = await asyncio.gather(*tasks)

        # 合并所有结果
        violations = []
        for group_result in group_results:
            violations.extend(group_result)

        # 更新性能统计
        total_time = time.time() - start_time
        self.total_validations += len(rules_to_validate)
        self.total_violations += len(violations)
        self.total_time += total_time

        logger.info(
            f"Validated {len(rules_to_validate)} rules in {len(rule_groups)} groups, "
            f"found {len(violations)} violations. Total time: {total_time:.4f}s",
        )

        return violations

    async def validate_rules_for_patient(self, patient_data: PatientData) -> list[RuleResult]:
        """
        Validates patient data against ALL rules loaded in the cache.
        Used by slave nodes for comprehensive validation.

        Args:
            patient_data: 患者数据

        Returns:
            list[RuleResult]: 违规规则的结果列表
        """
        # Get all rule IDs from the cache
        all_rule_ids = list(RULE_CACHE.keys())

        if not all_rule_ids:
            logger.warning("No rules found in cache for validation.")
            return []

        # Use the existing validate_rules method with all rule IDs
        return await self.validate_rules(patient_data, all_rule_ids)

    def close(self):
        """
        优雅地关闭进程池。
        """
        logger.info("Closing RuleService resources...")
        if self.use_dynamic_pool and self.dynamic_pool:
            # 动态进程池的关闭通过stop()方法异步处理
            logger.info("Dynamic process pool will be closed via stop() method")
        elif self.executor:
            self.executor.shutdown(wait=True)
            logger.info("ProcessPoolExecutor shut down successfully")

        # 输出性能统计
        if self.total_validations > 0:
            avg_time = self.total_time / self.total_validations
            violation_rate = self.total_violations / self.total_validations * 100
            logger.info(
                f"Performance stats - Total validations: {self.total_validations}, "
                f"Average time per rule: {avg_time*1000:.2f}ms, "
                f"Violation rate: {violation_rate:.1f}%",
            )

    def get_all_rules_info(self) -> list[dict[str, Any]]:
        """
        获取所有缓存中规则的基本信息
        """
        rules_info = []
        for key, instance in RULE_CACHE.items():
            # 假设规则实例有 rule_name 和 description 属性
            # 如果没有，需要从其他地方获取或在 BaseRule 中定义
            rule_name = getattr(instance, "rule_name", "N/A")
            description = getattr(instance, "description", "No description available.")
            rules_info.append({"rule_key": key, "rule_name": rule_name, "description": description})
        return rules_info

    def get_performance_stats(self) -> dict[str, Any]:
        """
        获取性能统计信息
        """
        stats = {
            "total_validations": self.total_validations,
            "total_violations": self.total_violations,
            "total_time": self.total_time,
            "avg_time_per_rule": self.total_time / max(1, self.total_validations),
            "violation_rate": self.total_violations / max(1, self.total_validations) * 100,
            "use_dynamic_pool": self.use_dynamic_pool,
            "use_memory_optimization": self.use_memory_optimization,
        }

        # 添加进程池统计
        if self.use_dynamic_pool and self.dynamic_pool:
            pool_stats = self.dynamic_pool.get_stats()
            stats.update(
                {
                    "current_workers": pool_stats.current_workers,
                    "active_tasks": pool_stats.active_tasks,
                    "completed_tasks": pool_stats.completed_tasks,
                    "failed_tasks": pool_stats.failed_tasks,
                    "avg_task_time": pool_stats.avg_task_time,
                },
            )

        # 添加性能监控数据
        if performance_monitor:
            current_metrics = performance_monitor.get_current_metrics()
            if current_metrics:
                stats.update({
                    "cpu_usage": current_metrics.cpu_usage,
                    "memory_usage": current_metrics.memory_usage,
                    "queue_length": current_metrics.queue_length,
                },
            )

        # 添加缓存统计
        if self.use_memory_optimization:
            cache_stats = intelligent_cache.get_overall_stats()
            stats["cache_stats"] = {
                name: {
                    "hit_rate": cache_stat.hit_rate,
                    "memory_usage_mb": cache_stat.memory_usage_mb,
                    "total_requests": cache_stat.total_requests,
                }
                for name, cache_stat in cache_stats.items()
            }

            # 添加对象池统计
            pool_stats = object_pool_manager.get_all_stats()
            stats["pool_stats"] = {
                name: {
                    "hit_rate": pool_stat.hit_rate,
                    "utilization_rate": pool_stat.utilization_rate,
                    "current_size": pool_stat.current_size,
                }
                for name, pool_stat in pool_stats.items()
            }

        return stats
