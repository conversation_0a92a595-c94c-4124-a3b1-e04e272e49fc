"""
动态进程池管理器
根据系统负载和任务队列长度动态调整进程池大小
"""

import asyncio
import os
import signal
import threading
import time
from collections.abc import Callable
from concurrent.futures import Future, ProcessPoolExecutor
from dataclasses import dataclass
from typing import Any

from config.settings import settings
from core.cpu_optimization import cpu_optimizer
from core.logging.error_enhancement import error_log_enhancer
from core.logging.logging_system import log as logger
from core.performance_monitor import performance_monitor
from core.rule_cache import RULE_CACHE
from services.rule_loader import load_rules_from_file, load_rules_into_cache


@dataclass
class PoolStats:
    """进程池统计信息"""

    current_workers: int
    active_tasks: int
    pending_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_task_time: float
    last_adjustment_time: float


def init_worker_with_preload():
    """
    工作进程初始化函数
    根据节点类型选择规则加载方式，忽略SIGINT信号
    - 主节点：从数据库加载规则
    - 子节点：从本地缓存文件加载规则
    """
    # 忽略SIGINT信号，让主进程处理优雅关闭
    signal.signal(signal.SIGINT, signal.SIG_IGN)

    # 根据节点类型选择规则加载方式
    try:
        # 首先初始化规则注册表
        logger.info(f"Worker process {os.getpid()} initializing rule registry...")
        from rules.rule_registry import rule_registry

        discovered_count = rule_registry.auto_discover_rules()
        logger.info(f"Worker process {os.getpid()} rule registry initialized with {discovered_count} rule classes")

        if settings.MODE == "slave":
            # 子节点：从本地缓存文件加载规则
            logger.info(f"Worker process {os.getpid()} loading rules from local cache file (slave mode)")
            success = asyncio.run(load_rules_from_file())
            if success:
                logger.info(f"Worker process {os.getpid()} initialized with {len(RULE_CACHE)} rules from local cache")
            else:
                logger.error(f"Worker process {os.getpid()} failed to load rules from local cache file")
                raise RuntimeError("Failed to load rules from local cache file")
        else:
            # 主节点：从数据库加载规则
            logger.info(f"Worker process {os.getpid()} loading rules from database (master mode)")
            load_rules_into_cache()
            logger.info(f"Worker process {os.getpid()} initialized with {len(RULE_CACHE)} rules from database")
    except Exception as e:
        logger.error(f"Failed to load rules in worker process {os.getpid()}: {e}")
        # 增强错误日志记录
        error_log_enhancer.enhance_error_logging(
            error=e,
            request_id=f"worker-init-{os.getpid()}",
            endpoint="worker_initialization",
            method="INIT",
            user_context={
                "worker_pid": os.getpid(),
                "operation": "rule_loading",
                "node_mode": settings.MODE,
                "loading_method": "local_cache" if settings.MODE == "slave" else "database",
            },
        )


class DynamicProcessPool:
    """
    动态进程池管理器
    根据系统负载自动调整进程池大小，优化资源利用率
    """

    def __init__(
        self,
        min_workers: int = 2,
        max_workers: int = 16,
        initial_workers: int | None = None,
        max_tasks_per_child: int = 100,
        adjustment_interval: float = 30.0,
        scale_up_threshold: float = 0.7,
        scale_down_threshold: float = 0.3,
    ):
        """
        初始化动态进程池

        Args:
            min_workers: 最小工作进程数
            max_workers: 最大工作进程数
            initial_workers: 初始工作进程数，None表示使用CPU核心数
            max_tasks_per_child: 每个子进程最大任务数，防止内存泄漏
            adjustment_interval: 调整间隔（秒）
            scale_up_threshold: 扩容阈值（CPU使用率）
            scale_down_threshold: 缩容阈值（CPU使用率）
        """
        self.min_workers = max(1, min_workers)
        self.max_workers = max(self.min_workers, max_workers)
        self.max_tasks_per_child = max_tasks_per_child
        self.adjustment_interval = adjustment_interval
        self.scale_up_threshold = scale_up_threshold
        self.scale_down_threshold = scale_down_threshold

        # 使用CPU优化器计算最优配置
        try:
            optimal_workers = cpu_optimizer.calculate_optimal_workers("cpu_intensive")
            logger.info(f"CPU optimizer suggests {optimal_workers} workers")

            # 调整最大工作进程数
            if self.max_workers == 16:  # 如果是默认值，使用优化建议
                self.max_workers = min(optimal_workers, 32)  # 限制最大值
        except Exception as e:
            logger.warning(f"Failed to get CPU optimization suggestions: {e}")

        # 初始工作进程数
        if initial_workers is None:
            self.current_workers = min(os.cpu_count() or 4, self.max_workers)
        else:
            self.current_workers = max(self.min_workers, min(initial_workers, self.max_workers))

        # 进程池和统计信息
        self.executor: ProcessPoolExecutor | None = None
        self.stats = PoolStats(
            current_workers=self.current_workers,
            active_tasks=0,
            pending_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            avg_task_time=0.0,
            last_adjustment_time=time.time(),
        )

        # 任务跟踪
        self.active_futures: dict[Future, float] = {}  # Future -> start_time
        self.task_times: list[float] = []  # 最近任务执行时间
        self.max_task_history = 100

        # 线程安全
        self._lock = threading.Lock()
        self._adjustment_task: asyncio.Task | None = None
        self._is_running = False

        logger.info(
            f"DynamicProcessPool initialized: workers={self.current_workers}, "
            f"range=[{self.min_workers}, {self.max_workers}]",
        )

        # 初始化降级适配器
        self._degradation_adapter = None
        self._init_degradation_adapter()

    async def start(self):
        """启动动态进程池"""
        if self._is_running:
            logger.warning("DynamicProcessPool is already running")
            return

        self._is_running = True

        # 创建初始进程池
        self._create_executor()

        # 启动性能监控
        performance_monitor.start_monitoring()

        # 启动自动调整任务
        self._adjustment_task = asyncio.create_task(self._adjustment_loop())

        logger.info("DynamicProcessPool started")

    async def stop(self):
        """停止动态进程池"""
        if not self._is_running:
            return

        self._is_running = False

        # 停止调整任务
        if self._adjustment_task:
            self._adjustment_task.cancel()
            try:
                await self._adjustment_task
            except asyncio.CancelledError:
                pass

        # 关闭进程池
        if self.executor:
            logger.info("Shutting down process pool...")
            self.executor.shutdown(wait=True)
            self.executor = None

        # 停止性能监控
        performance_monitor.stop_monitoring()

        logger.info("DynamicProcessPool stopped")

    def _init_degradation_adapter(self):
        """初始化降级适配器"""
        try:
            from core.degradation_adapters import DynamicProcessPoolAdapter, get_adapter_manager

            # 创建适配器
            self._degradation_adapter = DynamicProcessPoolAdapter(self)

            # 注册到适配器管理器
            adapter_manager = get_adapter_manager()
            adapter_manager.register_adapter(self._degradation_adapter)

            logger.info("DynamicProcessPool degradation adapter initialized and registered")

        except ImportError:
            logger.debug("Degradation adapters not available, skipping initialization")
        except Exception as e:
            logger.warning(f"Failed to initialize degradation adapter: {e}")

    def get_degradation_adapter(self):
        """获取降级适配器"""
        return self._degradation_adapter

    def _create_executor(self):
        """创建新的进程池执行器"""
        if self.executor:
            self.executor.shutdown(wait=False)

        self.executor = ProcessPoolExecutor(
            max_workers=self.current_workers,
            initializer=init_worker_with_preload,
            max_tasks_per_child=self.max_tasks_per_child,
        )

        with self._lock:
            self.stats.current_workers = self.current_workers

        logger.info(f"Created new ProcessPoolExecutor with {self.current_workers} workers")

    async def submit(self, fn: Callable, *args, **kwargs) -> Any:
        """
        提交任务到进程池

        Args:
            fn: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            任务执行结果
        """
        if not self.executor:
            raise RuntimeError("DynamicProcessPool is not started")

        # 更新队列长度
        with self._lock:
            self.stats.pending_tasks += 1
            performance_monitor.update_queue_length(self.stats.pending_tasks)

        # 提交任务
        loop = asyncio.get_running_loop()
        future = loop.run_in_executor(self.executor, fn, *args, **kwargs)

        # 记录任务开始时间
        start_time = time.time()
        with self._lock:
            self.active_futures[future] = start_time
            self.stats.active_tasks = len(self.active_futures)
            self.stats.pending_tasks -= 1

        try:
            result = await future

            # 记录任务完成
            with self._lock:
                if future in self.active_futures:
                    task_time = time.time() - self.active_futures[future]
                    del self.active_futures[future]

                    # 更新统计信息
                    self.task_times.append(task_time)
                    if len(self.task_times) > self.max_task_history:
                        self.task_times.pop(0)

                    self.stats.active_tasks = len(self.active_futures)
                    self.stats.completed_tasks += 1
                    self.stats.avg_task_time = sum(self.task_times) / len(self.task_times)

            return result

        except Exception as e:
            # 记录任务失败
            with self._lock:
                if future in self.active_futures:
                    del self.active_futures[future]
                    self.stats.active_tasks = len(self.active_futures)
                    self.stats.failed_tasks += 1

            # 增强错误日志记录
            error_log_enhancer.enhance_error_logging(
                error=e,
                request_id=f"task-{id(future)}",
                endpoint="dynamic_process_pool",
                method="EXECUTE",
                user_context={
                    "pool_stats": {
                        "active_tasks": self.stats.active_tasks,
                        "failed_tasks": self.stats.failed_tasks,
                        "current_workers": self.current_workers,
                    },
                    "operation": "task_execution",
                },
            )
            raise e

    async def _adjustment_loop(self):
        """自动调整循环"""
        while self._is_running:
            try:
                await asyncio.sleep(self.adjustment_interval)

                if not self._is_running:
                    break

                await self._adjust_pool_size()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in pool adjustment loop: {e}")
                # 增强错误日志记录
                error_log_enhancer.enhance_error_logging(
                    error=e,
                    request_id=f"pool-adjustment-{time.time()}",
                    endpoint="dynamic_process_pool",
                    method="ADJUST",
                    user_context={"pool_stats": self.get_stats().__dict__, "operation": "pool_adjustment"},
                )

    async def _adjust_pool_size(self):
        """调整进程池大小"""
        # 获取性能指标
        metrics = performance_monitor.get_current_metrics()
        if not metrics:
            return

        # 获取建议的工作进程数
        optimal_workers = performance_monitor.get_optimal_worker_count(
            min_workers=self.min_workers,
            max_workers=self.max_workers,
        )

        # 检查是否需要调整
        if optimal_workers == self.current_workers:
            return

        # 限制调整频率（避免频繁调整）
        current_time = time.time()
        if current_time - self.stats.last_adjustment_time < self.adjustment_interval:
            return

        # 执行调整
        old_workers = self.current_workers
        self.current_workers = optimal_workers

        # 重新创建进程池
        self._create_executor()

        with self._lock:
            self.stats.last_adjustment_time = current_time

        logger.info(
            f"Adjusted process pool size: {old_workers} -> {self.current_workers} workers "
            f"(CPU: {metrics.cpu_usage:.1f}%, Queue: {metrics.queue_length})",
        )

    def get_stats(self) -> PoolStats:
        """获取进程池统计信息"""
        with self._lock:
            return PoolStats(
                current_workers=self.stats.current_workers,
                active_tasks=self.stats.active_tasks,
                pending_tasks=self.stats.pending_tasks,
                completed_tasks=self.stats.completed_tasks,
                failed_tasks=self.stats.failed_tasks,
                avg_task_time=self.stats.avg_task_time,
                last_adjustment_time=self.stats.last_adjustment_time,
            )

    def force_adjust(self, worker_count: int):
        """强制调整工作进程数"""
        if not (self.min_workers <= worker_count <= self.max_workers):
            raise ValueError(f"Worker count must be between {self.min_workers} and {self.max_workers}")

        if worker_count == self.current_workers:
            return

        old_workers = self.current_workers
        self.current_workers = worker_count
        self._create_executor()

        logger.info(f"Force adjusted process pool size: {old_workers} -> {self.current_workers} workers")
