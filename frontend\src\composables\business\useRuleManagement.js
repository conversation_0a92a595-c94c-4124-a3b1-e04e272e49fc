/**
 * 规则管理业务逻辑Composable
 * 封装规则相关的业务操作和状态管理
 */
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useRulesStore } from '@/stores/rules'

/**
 * 规则管理主要功能
 */
export function useRuleManagement() {
  const router = useRouter()
  const rulesStore = useRulesStore()

  // 本地状态
  const selectedRuleKey = ref('')
  const detailDrawerVisible = ref(false)

  // Store状态 - 使用storeToRefs保持响应性，添加安全检查
  if (!rulesStore) {
    console.warn('[useRuleManagement] rulesStore not available, using default values')
    return {
      // 响应式数据 - 默认值
      rules: ref([]),
      loading: ref(false),
      statusCounts: ref({}),
      selectedRuleKey,
      detailDrawerVisible,
      availableStatuses: computed(() => []),
      rulesByStatus: computed(() => ({})),
      getRuleStats: computed(() => ({ total: 0, active: 0, deprecated: 0 })),

      // 方法 - 安全的默认实现
      formatStatus: (status) => status || 'UNKNOWN',
      fetchRules: async () => {
        console.warn('[useRuleManagement] Cannot fetch rules: store not available')
        return []
      },
      handleRefresh: async () => {
        console.warn('[useRuleManagement] Cannot refresh: store not available')
      },
      handleViewDetail: () => {
        console.warn('[useRuleManagement] Cannot view detail: store not available')
      },
      handleCloseDetail: () => {
        detailDrawerVisible.value = false
        selectedRuleKey.value = ''
      },
      handleDownloadTemplate: async () => {
        console.warn('[useRuleManagement] Cannot download template: store not available')
        return false
      },
      handleUploadData: () => {
        console.warn('[useRuleManagement] Cannot upload data: store not available')
      },
      getRuleByKey: () => null
    }
  }

  // 防护性检查，确保 store 存在且有效后再调用 storeToRefs
  let rules, loading, statusCounts
  try {
    if (rulesStore && typeof rulesStore === 'object' && rulesStore.$id) {
      const storeRefs = storeToRefs(rulesStore)
      rules = storeRefs.rules || ref([])
      loading = storeRefs.loading || ref(false)
      statusCounts = storeRefs.statusCounts || ref({})
    } else {
      // 如果 store 无效，使用默认值
      rules = ref([])
      loading = ref(false)
      statusCounts = ref({})
    }
  } catch (error) {
    console.warn('[useRuleManagement] storeToRefs failed, using default values:', error)
    rules = ref([])
    loading = ref(false)
    statusCounts = ref({})
  }

  // 状态映射
  const statusMap = {
    'NEW': '新规则',
    'CHANGED': '逻辑变更',
    'READY': '就绪',
    'DEPRECATED': '已弃用'
  }

  // 计算属性
  const availableStatuses = computed(() => {
    return Object.keys(statusCounts.value || {})
  })

  const rulesByStatus = computed(() => {
    const groups = {}
    if (rules.value && Array.isArray(rules.value)) {
      rules.value.forEach(rule => {
        const status = rule?.status || 'UNKNOWN'
        if (!groups[status]) {
          groups[status] = []
        }
        groups[status].push(rule)
      })
    }
    return groups
  })

  // 格式化状态显示
  const formatStatus = (status) => statusMap[status] || status

  // 获取规则列表
  const fetchRules = async () => {
    try {
      await rulesStore.fetchRules()
      return true
    } catch (error) {
      console.error('获取规则列表失败:', error)
      return false
    }
  }

  // 刷新规则列表
  const handleRefresh = async () => {
    try {
      await rulesStore.fetchRules()
      ElMessage.success('刷新成功')
      return true
    } catch (error) {
      ElMessage.error('刷新失败')
      return false
    }
  }

  // 查看规则详情
  const handleViewDetail = (rule) => {
    // 检查rule_key是否存在
    if (!rule?.rule_key) {
      ElMessage.error('规则键不存在，无法查看详情')
      return
    }
    // 跳转到规则模板详情页面
    router.push(`/rule-template-detail/${rule.rule_key}`)
  }

  // 关闭详情抽屉
  const handleCloseDetail = () => {
    detailDrawerVisible.value = false
    selectedRuleKey.value = ''
  }

  // 下载规则模板
  const handleDownloadTemplate = async (rule) => {
    // 检查rule_key是否存在
    if (!rule?.rule_key) {
      ElMessage.error('规则键不存在，无法下载模板')
      return false
    }

    try {
      await rulesStore.downloadTemplate(rule)
      ElMessage.success(`${rule.rule_name} 模板下载成功`)
      return true
    } catch (error) {
      ElMessage.error('模板下载失败')
      return false
    }
  }

  // 上传数据
  const handleUploadData = (rule) => {
    // 检查rule_key是否存在
    if (!rule?.rule_key) {
      ElMessage.error('规则键不存在，无法上传数据')
      return
    }
    router.push({
      name: 'DataUpload',
      params: { ruleKey: rule.rule_key }
    })
  }

  // 根据规则Key获取规则
  const getRuleByKey = (ruleKey) => {
    return rules.value?.find(rule => rule?.rule_key === ruleKey)
  }

  // 获取规则统计信息
  const getRuleStats = computed(() => {
    const total = rules.value?.length || 0
    const statusStats = {}

    if (rules.value && Array.isArray(rules.value)) {
      rules.value.forEach(rule => {
        const status = rule?.status || 'UNKNOWN'
        statusStats[status] = (statusStats[status] || 0) + 1
      })
    }

    return {
      total,
      statusStats,
      readyCount: statusStats.READY || 0,
      newCount: statusStats.NEW || 0,
      changedCount: statusStats.CHANGED || 0,
      deprecatedCount: statusStats.DEPRECATED || 0
    }
  })

  return {
    // 响应式数据
    rules,
    loading,
    statusCounts,
    selectedRuleKey,
    detailDrawerVisible,
    availableStatuses,
    rulesByStatus,
    getRuleStats,

    // 方法
    formatStatus,
    fetchRules,
    handleRefresh,
    handleViewDetail,
    handleCloseDetail,
    handleDownloadTemplate,
    handleUploadData,
    getRuleByKey
  }
}

/**
 * 规则状态管理
 */
export function useRuleStatus() {
  // 状态颜色映射
  const statusColors = {
    'NEW': '#409EFF',
    'CHANGED': '#E6A23C',
    'READY': '#67C23A',
    'DEPRECATED': '#F56C6C'
  }

  // 状态图标映射
  const statusIcons = {
    'NEW': 'Plus',
    'CHANGED': 'Edit',
    'READY': 'Check',
    'DEPRECATED': 'Close'
  }

  // 获取状态颜色
  const getStatusColor = (status) => {
    return statusColors[status] || '#909399'
  }

  // 获取状态图标
  const getStatusIcon = (status) => {
    return statusIcons[status] || 'QuestionFilled'
  }

  // 判断状态是否可操作
  const isStatusActionable = (status) => {
    return ['NEW', 'CHANGED'].includes(status)
  }

  // 判断状态是否就绪
  const isStatusReady = (status) => {
    return status === 'READY'
  }

  return {
    statusColors,
    statusIcons,
    getStatusColor,
    getStatusIcon,
    isStatusActionable,
    isStatusReady
  }
}

/**
 * 规则操作权限管理
 */
export function useRulePermissions() {
  // 检查是否可以下载模板
  const canDownloadTemplate = (rule) => {
    return rule && ['NEW', 'CHANGED', 'READY'].includes(rule.status)
  }

  // 检查是否可以上传数据
  const canUploadData = (rule) => {
    return rule && ['NEW', 'CHANGED'].includes(rule.status)
  }

  // 检查是否可以查看详情
  const canViewDetail = (rule) => {
    return rule && rule.rule_key
  }

  // 检查是否可以编辑规则
  const canEditRule = (rule) => {
    return rule && ['NEW', 'CHANGED'].includes(rule.status)
  }

  return {
    canDownloadTemplate,
    canUploadData,
    canViewDetail,
    canEditRule
  }
}
