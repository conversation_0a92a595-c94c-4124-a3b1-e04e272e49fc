# 配置说明

本文档详细说明规则验证系统的所有配置参数，帮助用户正确配置系统以满足不同环境需求。

## 📋 配置文件结构

系统支持多种配置方式：
1. **环境变量** (.env文件)
2. **配置文件** (config/settings.py)
3. **命令行参数**

优先级：命令行参数 > 环境变量 > 配置文件默认值

## ⚙️ 核心配置参数

### 应用基础配置

```env
# 应用运行模式
MODE=master                    # master(主节点) 或 slave(从节点)

# 服务器配置
SERVER_HOST=0.0.0.0           # 服务监听地址
SERVER_PORT=18001             # 服务端口 (主节点:18001, 从节点:18002)

# 应用环境
ENVIRONMENT=production        # development, testing, production
DEBUG=false                   # 调试模式开关
```

### 数据库配置

```env
# 数据库连接
DATABASE_URL=mysql://username:password@host:port/database_name

# 或者分别配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=rule_user
DB_PASSWORD=your_password
DB_NAME=rule_service
DB_DRIVER=pymysql

# 连接池配置
DB_POOL_SIZE=20               # 连接池大小
DB_MAX_OVERFLOW=30            # 最大溢出连接数
DB_POOL_TIMEOUT=30            # 连接超时时间(秒)
DB_POOL_RECYCLE=3600          # 连接回收时间(秒)
```

### 日志配置

```env
# 日志级别
LOG_LEVEL=INFO                # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 日志输出
LOG_STDOUT_ENABLED=true       # 控制台输出
LOG_FILE_ENABLED=true         # 文件输出
LOG_FILE_PATH=logs/app.log    # 日志文件路径

# 日志格式
LOG_FORMAT=detailed           # simple, detailed, json

# 日志轮转
LOG_MAX_SIZE=100MB            # 单个日志文件最大大小
LOG_BACKUP_COUNT=10           # 保留的日志文件数量
```

## 🔄 主从同步配置

### 主节点配置

```env
# 主节点特有配置
ENABLE_WEB_UI=true            # 启用Web界面
WEB_UI_PORT=18001             # Web界面端口

# API安全配置
API_KEY_REQUIRED=true         # 是否需要API密钥
MASTER_API_KEY=your_master_api_key  # 主节点API密钥

# 同步服务配置
SYNC_SERVICE_ENABLED=true     # 启用同步服务
SYNC_ENDPOINT=/api/v1/sync    # 同步接口路径
```

### 从节点配置

```env
# 规则同步配置
ENABLE_RULE_SYNC=true         # 启用规则同步
MASTER_API_ENDPOINT=http://master-node:18001  # 主节点地址
SLAVE_API_KEY=your_slave_api_key              # 从节点API密钥

# 同步参数
RULE_SYNC_INTERVAL=60         # 同步间隔(秒)
RULE_SYNC_TIMEOUT=120.0       # 同步超时时间(秒)
RULE_SYNC_MAX_RETRIES=3       # 最大重试次数
RULE_SYNC_RETRY_INTERVAL=30.0 # 重试间隔(秒)

# 离线模式配置
OFFLINE_MODE=false            # 离线模式开关
RULE_CACHE_PATH=rules_cache.json.gz  # 规则缓存文件路径
```

## 🚀 性能优化配置

### 并发处理配置

```env
# 工作进程配置
WORKER_COUNT=16               # 工作进程数量 (建议为CPU核心数的2倍)
QUEUE_MAX_SIZE=2000           # 任务队列最大大小
BATCH_SIZE=100                # 批处理大小

# 线程池配置
THREAD_POOL_SIZE=32           # 线程池大小
MAX_WORKERS=64                # 最大工作线程数
```

### 缓存配置

```env
# 缓存启用
CACHE_ENABLED=true            # 启用缓存
CACHE_TYPE=memory             # memory, redis, memcached

# 内存缓存配置
CACHE_MAX_SIZE=1000           # 最大缓存条目数
CACHE_TTL=3600                # 缓存过期时间(秒)

# Redis缓存配置 (如果使用Redis)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
```

### 性能监控配置

```env
# 性能监控
PERFORMANCE_MONITORING_ENABLED=true  # 启用性能监控
METRICS_COLLECTION_INTERVAL=60       # 指标收集间隔(秒)
PERFORMANCE_LOG_ENABLED=true         # 性能日志

# 资源限制
MAX_MEMORY_USAGE=4GB          # 最大内存使用
MAX_CPU_USAGE=80              # 最大CPU使用率(%)
```

## 🛡️ 安全配置

### API安全

```env
# API认证
API_KEY_HEADER=X-API-KEY      # API密钥头部名称
API_RATE_LIMIT=1000           # API速率限制(每分钟请求数)

# CORS配置
CORS_ENABLED=true             # 启用CORS
CORS_ORIGINS=*                # 允许的源地址
CORS_METHODS=GET,POST,PUT,DELETE  # 允许的HTTP方法
```

### 数据安全

```env
# 数据加密
DATA_ENCRYPTION_ENABLED=false # 数据加密开关
ENCRYPTION_KEY=your_encryption_key  # 加密密钥

# 敏感数据处理
MASK_SENSITIVE_DATA=true      # 遮蔽敏感数据
LOG_SENSITIVE_DATA=false      # 日志中记录敏感数据
```

## 🔧 环境特定配置

### 开发环境

```env
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
RELOAD_ON_CHANGE=true         # 代码变更自动重载
```

### 测试环境

```env
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=INFO
DATABASE_URL=mysql://test_user:test_pass@test_host:3306/test_db
```

### 生产环境

```env
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING
PERFORMANCE_MONITORING_ENABLED=true
API_RATE_LIMIT=500
```

## 📝 配置验证

### 配置检查命令

```bash
# 验证配置文件
python -m config.validation

# 检查数据库连接
python -m tools.check_database

# 验证API配置
python -m tools.validate_api_config
```

### 常见配置错误

1. **数据库连接失败**
   - 检查数据库地址和端口
   - 验证用户名和密码
   - 确认数据库存在

2. **端口冲突**
   - 检查端口是否被占用
   - 修改SERVER_PORT配置

3. **同步失败**
   - 验证主节点地址配置
   - 检查API密钥是否正确
   - 确认网络连通性

## 📋 配置模板

### 主节点配置模板

```env
# 主节点配置示例
MODE=master
SERVER_HOST=0.0.0.0
SERVER_PORT=18001
DATABASE_URL=mysql://rule_user:password@localhost:3306/rule_service
LOG_LEVEL=INFO
ENABLE_WEB_UI=true
API_KEY_REQUIRED=true
MASTER_API_KEY=master_secret_key_2025
WORKER_COUNT=16
PERFORMANCE_MONITORING_ENABLED=true
```

### 从节点配置模板

```env
# 从节点配置示例
MODE=slave
SERVER_HOST=0.0.0.0
SERVER_PORT=18002
LOG_LEVEL=INFO
ENABLE_RULE_SYNC=true
MASTER_API_ENDPOINT=http://master-server:18001
SLAVE_API_KEY=slave_secret_key_2025
RULE_SYNC_INTERVAL=60
WORKER_COUNT=32
PERFORMANCE_MONITORING_ENABLED=true
```

---

**配置建议**: 
- 生产环境建议使用环境变量而非配置文件存储敏感信息
- 定期检查和更新API密钥
- 根据实际负载调整性能参数
- 启用监控以便及时发现配置问题

**更新时间**: 2025-07-23
