"""
降级管理器核心实现
负责监控系统状态、触发降级决策、协调各组件降级动作、管理降级状态转换
"""

import asyncio
import threading
import time
from collections import deque
from dataclasses import dataclass

from config.degradation_config import DegradationConfig, get_degradation_config
from core.degradation_core import (
    DegradationEvent,
    DegradationEventManager,
    DegradationEventType,
    DegradationLevel,
    DegradationStateMachine,
    DegradationStrategyManager,
    DegradationTriggerType,
)
from core.logging.logging_system import log as logger
from core.performance_monitor import PerformanceMonitor, SystemMetrics


@dataclass
class TriggerEvaluation:
    """触发条件评估结果"""

    trigger_type: DegradationTriggerType
    current_value: float
    threshold: float
    recovery_threshold: float
    is_triggered: bool
    is_recovered: bool
    confidence: float = 0.0  # 置信度 (0.0-1.0)
    sample_count: int = 0

    def __post_init__(self):
        """计算置信度"""
        if self.sample_count > 0:
            # 基于样本数量计算置信度
            max_samples = 10  # 假设最大样本数为10
            self.confidence = min(self.sample_count / max_samples, 1.0)


class DegradationDecisionEngine:
    """降级决策引擎

    基于多维度指标进行综合判断，决定是否需要降级或恢复
    """

    def __init__(self, config: DegradationConfig):
        self.config = config
        self._evaluation_history: deque = deque(maxlen=100)

    def evaluate_triggers(self, metrics: SystemMetrics, error_rate: float = 0.0) -> list[TriggerEvaluation]:
        """
        评估所有触发条件

        Args:
            metrics: 系统性能指标
            error_rate: 错误率（百分比）

        Returns:
            List[TriggerEvaluation]: 触发条件评估结果列表
        """
        evaluations = []
        thresholds = self.config.thresholds

        # CPU使用率评估
        cpu_eval = self._evaluate_single_trigger(
            DegradationTriggerType.CPU_USAGE,
            metrics.cpu_usage,
            [thresholds.cpu_l1_threshold, thresholds.cpu_l2_threshold, thresholds.cpu_l3_threshold],
            [thresholds.cpu_l1_recovery, thresholds.cpu_l2_recovery, thresholds.cpu_l3_recovery],
        )
        evaluations.extend(cpu_eval)

        # 内存使用率评估
        memory_eval = self._evaluate_single_trigger(
            DegradationTriggerType.MEMORY_USAGE,
            metrics.memory_usage,
            [thresholds.memory_l1_threshold, thresholds.memory_l2_threshold, thresholds.memory_l3_threshold],
            [thresholds.memory_l1_recovery, thresholds.memory_l2_recovery, thresholds.memory_l3_recovery],
        )
        evaluations.extend(memory_eval)

        # 错误率评估
        error_eval = self._evaluate_single_trigger(
            DegradationTriggerType.ERROR_RATE,
            error_rate,
            [thresholds.error_l1_threshold, thresholds.error_l2_threshold, thresholds.error_l3_threshold],
            [thresholds.error_l1_recovery, thresholds.error_l2_recovery, thresholds.error_l3_recovery],
        )
        evaluations.extend(error_eval)

        # 队列长度评估
        queue_eval = self._evaluate_single_trigger(
            DegradationTriggerType.QUEUE_LENGTH,
            float(metrics.queue_length),
            [
                float(thresholds.queue_l1_threshold),
                float(thresholds.queue_l2_threshold),
                float(thresholds.queue_l3_threshold),
            ],
            [
                float(thresholds.queue_l1_recovery),
                float(thresholds.queue_l2_recovery),
                float(thresholds.queue_l3_recovery),
            ],
        )
        evaluations.extend(queue_eval)

        # 记录评估历史
        self._evaluation_history.append(
            {"timestamp": time.time(), "evaluations": evaluations, "metrics": metrics, "error_rate": error_rate}
        )

        return evaluations

    def _evaluate_single_trigger(
        self,
        trigger_type: DegradationTriggerType,
        current_value: float,
        thresholds: list[float],
        recovery_thresholds: list[float],
    ) -> list[TriggerEvaluation]:
        """评估单个触发条件的所有级别"""
        evaluations = []

        for i in range(len(thresholds)):
            threshold = thresholds[i]
            recovery_threshold = recovery_thresholds[i]

            is_triggered = current_value >= threshold
            is_recovered = current_value <= recovery_threshold

            evaluation = TriggerEvaluation(
                trigger_type=trigger_type,
                current_value=current_value,
                threshold=threshold,
                recovery_threshold=recovery_threshold,
                is_triggered=is_triggered,
                is_recovered=is_recovered,
                sample_count=len(self._evaluation_history),
            )

            evaluations.append(evaluation)

        return evaluations

    def decide_degradation_level(
        self, evaluations: list[TriggerEvaluation], current_level: DegradationLevel
    ) -> DegradationLevel:
        """
        基于评估结果决定降级级别

        Args:
            evaluations: 触发条件评估结果
            current_level: 当前降级级别

        Returns:
            DegradationLevel: 建议的降级级别
        """
        # 按级别分组评估结果
        level_triggers = {
            DegradationLevel.L1_LIGHT: [],
            DegradationLevel.L2_MODERATE: [],
            DegradationLevel.L3_SEVERE: [],
        }

        levels = [DegradationLevel.L1_LIGHT, DegradationLevel.L2_MODERATE, DegradationLevel.L3_SEVERE]

        # 将评估结果按级别分组
        for i, level in enumerate(levels):
            for eval_result in evaluations:
                if i < len(evaluations) // len(levels):  # 简化的分组逻辑
                    level_triggers[level].append(eval_result)

        # 从高级别开始检查是否需要降级
        for level in reversed(levels):
            triggered_count = sum(1 for eval_result in level_triggers[level] if eval_result.is_triggered)
            total_count = len(level_triggers[level])

            if total_count > 0 and triggered_count > 0:
                # 如果有任何触发条件满足，考虑降级到该级别
                if level.value > current_level.value:  # 只能升级降级级别
                    return level

        # 检查是否可以恢复
        if current_level != DegradationLevel.L0_NORMAL:
            # 检查当前级别的恢复条件
            current_level_index = levels.index(current_level) if current_level in levels else -1
            if current_level_index >= 0:
                current_level_evals = level_triggers[current_level]
                recovered_count = sum(1 for eval_result in current_level_evals if eval_result.is_recovered)
                total_count = len(current_level_evals)

                if total_count > 0 and recovered_count == total_count:
                    # 所有条件都满足恢复要求
                    if current_level_index > 0:
                        return levels[current_level_index - 1]  # 降级一个级别
                    else:
                        return DegradationLevel.L0_NORMAL  # 恢复正常

        return current_level  # 保持当前级别


class DegradationManager:
    """降级管理器核心类

    负责监控系统状态、触发降级决策、协调各组件降级动作、管理降级状态转换
    """

    def __init__(self, performance_monitor: PerformanceMonitor | None = None):
        """
        初始化降级管理器

        Args:
            performance_monitor: 性能监控器实例，如果为None则创建新实例
        """
        self.config = get_degradation_config()
        self.performance_monitor = performance_monitor or PerformanceMonitor()

        # 核心组件
        self.state_machine = DegradationStateMachine()
        self.event_manager = DegradationEventManager()
        self.strategy_manager = DegradationStrategyManager()
        self.decision_engine = DegradationDecisionEngine(self.config)

        # 运行状态
        self._is_running = False
        self._monitor_task: asyncio.Task | None = None
        self._lock = threading.RLock()

        # 错误率统计
        self._error_count = 0
        self._total_requests = 0
        self._error_history: deque = deque(maxlen=self.config.stats_window_size)

        # 降级预热机制
        self._last_degradation_time = 0.0
        self._degradation_cooldown = self.config.min_duration

        logger.info("DegradationManager initialized")

    async def start(self):
        """启动降级管理器"""
        if self._is_running:
            logger.warning("DegradationManager is already running")
            return

        if not self.config.enabled:
            logger.info("Degradation mechanism is disabled")
            return

        self._is_running = True

        # 启动性能监控
        if not self.performance_monitor.is_monitoring:
            self.performance_monitor.start_monitoring()

        # 启动监控任务
        self._monitor_task = asyncio.create_task(self._monitoring_loop())

        logger.info("DegradationManager started")

    async def stop(self):
        """停止降级管理器"""
        if not self._is_running:
            return

        self._is_running = False

        # 停止监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("DegradationManager stopped")

    async def _monitoring_loop(self):
        """监控循环"""
        while self._is_running:
            try:
                await self._check_and_process_degradation()
                await asyncio.sleep(self.config.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Degradation monitoring loop error: {e}", exc_info=True)
                await asyncio.sleep(self.config.check_interval)

    async def _check_and_process_degradation(self):
        """检查并处理降级"""
        # 获取当前系统指标
        metrics = self.performance_monitor.get_current_metrics()
        if not metrics:
            return

        # 计算错误率
        error_rate = self._calculate_error_rate()

        # 评估触发条件
        evaluations = self.decision_engine.evaluate_triggers(metrics, error_rate)

        # 获取当前状态
        current_state = self.state_machine.get_current_state()
        current_level = current_state.current_level

        # 决定目标降级级别
        target_level = self.decision_engine.decide_degradation_level(evaluations, current_level)

        # 检查是否需要状态转换
        if target_level != current_level:
            await self._handle_level_change(current_level, target_level, evaluations)

    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        with self._lock:
            if self._total_requests == 0:
                return 0.0
            return (self._error_count / self._total_requests) * 100.0

    async def _handle_level_change(
        self, current_level: DegradationLevel, target_level: DegradationLevel, evaluations: list[TriggerEvaluation]
    ):
        """
        处理降级级别变更

        Args:
            current_level: 当前降级级别
            target_level: 目标降级级别
            evaluations: 触发条件评估结果
        """
        # 检查降级冷却时间
        if target_level != DegradationLevel.L0_NORMAL:
            current_time = time.time()
            if current_time - self._last_degradation_time < self._degradation_cooldown:
                logger.debug("Degradation cooldown active, skipping level change")
                return

        # 确定触发类型
        trigger_type = self._determine_trigger_type(evaluations, target_level)

        # 执行状态转换
        success = self.state_machine.transition_to(
            target_level=target_level, trigger_type=trigger_type, is_manual=False
        )

        if not success:
            logger.warning(f"Failed to transition from {current_level.value} to {target_level.value}")
            return

        # 更新降级时间
        if target_level != DegradationLevel.L0_NORMAL:
            self._last_degradation_time = time.time()

        # 执行降级动作
        await self._execute_degradation_actions(target_level, trigger_type)

        # 发布降级事件
        event_type = (
            DegradationEventType.DEGRADATION_TRIGGERED
            if target_level != DegradationLevel.L0_NORMAL
            else DegradationEventType.DEGRADATION_RECOVERED
        )

        event = DegradationEvent(
            event_type=event_type,
            timestamp=time.time(),
            level=target_level,
            trigger_type=trigger_type,
            trigger_value=self._get_trigger_value(evaluations, trigger_type),
            metadata={
                "previous_level": current_level.value,
                "evaluations": [
                    {
                        "trigger_type": eval.trigger_type.value,
                        "current_value": eval.current_value,
                        "threshold": eval.threshold,
                        "is_triggered": eval.is_triggered,
                        "confidence": eval.confidence,
                    }
                    for eval in evaluations
                ],
            },
        )

        self.event_manager.publish_event(event)

        logger.info(
            f"Degradation level changed: {current_level.value} -> {target_level.value}",
            extra={
                "previous_level": current_level.value,
                "new_level": target_level.value,
                "trigger_type": trigger_type.value if trigger_type else None,
                "event_type": event_type.value,
            },
        )

    def _determine_trigger_type(
        self, evaluations: list[TriggerEvaluation], target_level: DegradationLevel
    ) -> DegradationTriggerType | None:
        """确定主要触发类型"""
        if target_level == DegradationLevel.L0_NORMAL:
            return None

        # 找到置信度最高的触发条件
        triggered_evals = [eval for eval in evaluations if eval.is_triggered]
        if not triggered_evals:
            return None

        # 按置信度排序，返回最高的
        triggered_evals.sort(key=lambda x: x.confidence, reverse=True)
        return triggered_evals[0].trigger_type

    def _get_trigger_value(
        self, evaluations: list[TriggerEvaluation], trigger_type: DegradationTriggerType | None
    ) -> float | None:
        """获取触发值"""
        if not trigger_type:
            return None

        for eval in evaluations:
            if eval.trigger_type == trigger_type:
                return eval.current_value

        return None

    async def _execute_degradation_actions(
        self, level: DegradationLevel, trigger_type: DegradationTriggerType | None
    ):
        """
        执行降级动作

        Args:
            level: 降级级别
            trigger_type: 触发类型
        """
        strategy = self.strategy_manager.get_strategy(level)
        if not strategy or not strategy.enabled:
            logger.warning(f"No strategy found or disabled for level {level.value}")
            return

        # 按优先级执行动作
        for action in strategy.actions:
            try:
                # 创建动作执行事件
                action_event = DegradationEvent(
                    event_type=DegradationEventType.ACTION_EXECUTED,
                    timestamp=time.time(),
                    level=level,
                    trigger_type=trigger_type,
                    actions=[action],
                    metadata={
                        "action_type": action.action_type.value,
                        "target_component": action.target_component,
                        "parameters": action.parameters,
                    },
                )

                # 发布动作事件（组件会响应此事件）
                self.event_manager.publish_event(action_event)

                # 记录已执行的动作
                self.state_machine.add_executed_action(action)

                logger.info(
                    f"Degradation action executed: {action.action_type.value} on {action.target_component}",
                    extra={
                        "action_type": action.action_type.value,
                        "target_component": action.target_component,
                        "parameters": action.parameters,
                        "level": level.value,
                    },
                )

            except Exception as e:
                logger.error(
                    f"Failed to execute degradation action: {action.action_type.value}",
                    extra={
                        "action_type": action.action_type.value,
                        "target_component": action.target_component,
                        "error": str(e),
                    },
                    exc_info=True,
                )

                # 发布动作失败事件
                failure_event = DegradationEvent(
                    event_type=DegradationEventType.ACTION_FAILED,
                    timestamp=time.time(),
                    level=level,
                    trigger_type=trigger_type,
                    actions=[action],
                    metadata={
                        "action_type": action.action_type.value,
                        "target_component": action.target_component,
                        "error": str(e),
                    },
                )
                self.event_manager.publish_event(failure_event)

    def record_request_result(self, is_error: bool = False):
        """
        记录请求结果，用于错误率统计

        Args:
            is_error: 是否为错误请求
        """
        with self._lock:
            self._total_requests += 1
            if is_error:
                self._error_count += 1

            # 记录到历史中
            self._error_history.append({"timestamp": time.time(), "is_error": is_error})

            # 清理过期的历史记录
            current_time = time.time()
            window_seconds = self.config.metrics_retention
            while self._error_history and current_time - self._error_history[0]["timestamp"] > window_seconds:
                old_record = self._error_history.popleft()
                if old_record["is_error"]:
                    self._error_count = max(0, self._error_count - 1)
                self._total_requests = max(0, self._total_requests - 1)

    def manual_trigger_degradation(self, level: DegradationLevel, reason: str = "Manual trigger") -> bool:
        """
        手动触发降级

        Args:
            level: 目标降级级别
            reason: 触发原因

        Returns:
            bool: 是否成功触发
        """
        try:
            # 执行状态转换
            success = self.state_machine.transition_to(
                target_level=level, trigger_type=DegradationTriggerType.MANUAL, is_manual=True, reason=reason
            )

            if not success:
                return False

            # 发布手动干预事件
            event = DegradationEvent(
                event_type=DegradationEventType.MANUAL_OVERRIDE,
                timestamp=time.time(),
                level=level,
                trigger_type=DegradationTriggerType.MANUAL,
                metadata={"reason": reason, "is_manual": True},
            )

            self.event_manager.publish_event(event)

            logger.info(
                f"Manual degradation triggered: {level.value}",
                extra={"level": level.value, "reason": reason, "is_manual": True},
            )

            return True

        except Exception as e:
            logger.error(f"Failed to manually trigger degradation: {e}", exc_info=True)
            return False

    def manual_recover(self, reason: str = "Manual recovery") -> bool:
        """
        手动恢复正常状态

        Args:
            reason: 恢复原因

        Returns:
            bool: 是否成功恢复
        """
        return self.manual_trigger_degradation(DegradationLevel.L0_NORMAL, reason)

    def get_current_status(self) -> dict[str, any]:
        """
        获取当前降级状态信息

        Returns:
            Dict[str, any]: 当前状态信息
        """
        state = self.state_machine.get_current_state()
        metrics = self.performance_monitor.get_current_metrics()
        error_rate = self._calculate_error_rate()

        return {
            "enabled": self.config.enabled,
            "is_running": self._is_running,
            "current_level": state.current_level.value,
            "previous_level": state.previous_level.value,
            "is_degraded": state.is_degraded(),
            "degradation_duration": state.get_degradation_duration(),
            "last_change_time": state.last_change_time,
            "active_triggers": [trigger.value for trigger in state.active_triggers],
            "is_manual_override": state.is_manual_override,
            "override_reason": state.override_reason,
            "executed_actions_count": len(state.executed_actions),
            "current_metrics": {
                "cpu_usage": metrics.cpu_usage if metrics else 0.0,
                "memory_usage": metrics.memory_usage if metrics else 0.0,
                "queue_length": metrics.queue_length if metrics else 0,
                "error_rate": error_rate,
            },
            "thresholds": {
                "cpu_l1": self.config.thresholds.cpu_l1_threshold,
                "cpu_l2": self.config.thresholds.cpu_l2_threshold,
                "cpu_l3": self.config.thresholds.cpu_l3_threshold,
                "memory_l1": self.config.thresholds.memory_l1_threshold,
                "memory_l2": self.config.thresholds.memory_l2_threshold,
                "memory_l3": self.config.thresholds.memory_l3_threshold,
                "error_l1": self.config.thresholds.error_l1_threshold,
                "error_l2": self.config.thresholds.error_l2_threshold,
                "error_l3": self.config.thresholds.error_l3_threshold,
            },
        }

    def get_degradation_history(self, limit: int = 50) -> list[dict[str, any]]:
        """
        获取降级历史记录

        Args:
            limit: 返回记录数量限制

        Returns:
            List[Dict[str, any]]: 降级历史记录
        """
        return self.state_machine.get_transition_history(limit)

    def get_event_history(self, limit: int = 100) -> list[DegradationEvent]:
        """
        获取降级事件历史

        Args:
            limit: 返回事件数量限制

        Returns:
            List[DegradationEvent]: 事件历史记录
        """
        return self.event_manager.get_event_history(limit)

    def get_metrics(self) -> dict[str, any]:
        """
        获取降级指标统计

        Returns:
            Dict[str, any]: 指标统计信息
        """
        metrics = self.event_manager.get_metrics()

        return {
            "total_degradations": metrics.total_degradations,
            "degradation_by_level": {level.value: count for level, count in metrics.degradation_by_level.items()},
            "degradation_by_trigger": {
                trigger.value: count for trigger, count in metrics.degradation_by_trigger.items()
            },
            "total_recovery_time": metrics.total_recovery_time,
            "average_recovery_time": metrics.average_recovery_time,
            "last_degradation_time": metrics.last_degradation_time,
            "successful_actions": metrics.successful_actions,
            "failed_actions": metrics.failed_actions,
            "action_success_rate": (
                metrics.successful_actions / (metrics.successful_actions + metrics.failed_actions) * 100
                if (metrics.successful_actions + metrics.failed_actions) > 0
                else 0.0
            ),
            "error_statistics": {
                "total_requests": self._total_requests,
                "error_count": self._error_count,
                "current_error_rate": self._calculate_error_rate(),
            },
        }

    def register_component(self, component):
        """
        注册降级感知组件

        Args:
            component: 实现了DegradationAware接口的组件
        """
        self.event_manager.register_component(component)
        logger.info(f"Component registered: {component.get_component_name()}")

    def unregister_component(self, component):
        """
        取消注册降级感知组件

        Args:
            component: 要取消注册的组件
        """
        self.event_manager.unregister_component(component)
        logger.info(f"Component unregistered: {component.get_component_name()}")

    def get_registered_components(self) -> dict[str, int]:
        """
        获取已注册的组件信息

        Returns:
            Dict[str, int]: 组件名称到实例数量的映射
        """
        return self.event_manager.get_registered_components()

    def is_enabled(self) -> bool:
        """检查降级功能是否启用"""
        return self.config.enabled

    def is_running(self) -> bool:
        """检查降级管理器是否正在运行"""
        return self._is_running

    def refresh_config(self):
        """刷新配置"""
        from config.degradation_config import get_degradation_config

        self.config = get_degradation_config(force_refresh=True)
        logger.info("Degradation configuration refreshed")


# 全局降级管理器实例
_degradation_manager: DegradationManager | None = None


def get_degradation_manager(performance_monitor: PerformanceMonitor | None = None) -> DegradationManager:
    """
    获取全局降级管理器实例

    Args:
        performance_monitor: 性能监控器实例

    Returns:
        DegradationManager: 降级管理器实例
    """
    global _degradation_manager

    if _degradation_manager is None:
        _degradation_manager = DegradationManager(performance_monitor)

    return _degradation_manager


async def start_degradation_manager(performance_monitor: PerformanceMonitor | None = None):
    """
    启动全局降级管理器的便捷函数

    Args:
        performance_monitor: 性能监控器实例
    """
    manager = get_degradation_manager(performance_monitor)
    await manager.start()


async def stop_degradation_manager():
    """停止全局降级管理器的便捷函数"""
    global _degradation_manager

    if _degradation_manager:
        await _degradation_manager.stop()
