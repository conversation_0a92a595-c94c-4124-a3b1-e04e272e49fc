"""
Sync router for master node - handles slave synchronization.
"""

import gzip
import hashlib
import json
from datetime import datetime

from fastapi import APIRouter, HTTPException, Request, Response

from api.dependencies.auth import get_api_key_dependency
from core.constants.error_codes import ErrorCodes
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from core.rule_cache import RULE_CACHE
from models.api import ApiResponse, RuleVersionResponse

# Sync router (requires API key authentication for slave nodes)
sync_router = APIRouter(
    prefix="/api/v1/rules",
    tags=["Rule Sync (Secure)"],
    dependencies=[get_api_key_dependency()],
)


@sync_router.get("/version", response_model=ApiResponse[RuleVersionResponse])
def get_rules_version(request: Request):
    """
    Get the current version hash of all active rules.

    Args:
        request: HTTP request object

    Returns:
        ApiResponse[RuleVersionResponse]: Unified response with version hash of current rule set
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting rules version: {request_id}")

        # Add request tracking event
        if request_id:
            request_tracker.add_event(
                request_id,
                "version_request",
                {"endpoint": "/api/v1/rules/version", "operation": "get_version"},
            )

        if not RULE_CACHE:
            logger.warning(f"No rules loaded in cache: {request_id}")
            version_response = RuleVersionResponse(version="no_rules_loaded")

            return ApiResponse.success_response(
                data=version_response,
                message="规则版本获取成功（无规则加载）",
                request_id=request_id,
            )

        sorted_keys = sorted(RULE_CACHE.keys())
        version_string = "".join(sorted_keys)
        version_hash = hashlib.sha256(version_string.encode("utf-8")).hexdigest()

        version_response = RuleVersionResponse(version=version_hash)

        logger.info(f"Rules version generated: {request_id} - {version_hash[:8]}...")

        # Add completion event
        if request_id:
            request_tracker.add_event(
                request_id,
                "version_generated",
                {"version_hash": version_hash[:8], "rules_count": len(RULE_CACHE)},
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(
            data=version_response, message=f"规则版本获取成功，当前加载 {len(RULE_CACHE)} 个规则", request_id=request_id
        )

    except Exception as e:
        logger.error(f"Error getting rules version: {request_id} - {str(e)}", exc_info=True)

        # Add error event
        if request_id:
            request_tracker.add_event(
                request_id,
                "version_error",
                {"error_type": type(e).__name__, "error_message": str(e)},
            )
            request_tracker.complete_request(request_id, success=False)

        # Re-raise to let unified error handler process it
        raise


@sync_router.get("/export", response_class=Response)
def export_rules_package(request: Request):
    """
    Export all active rules as a compressed package for slave nodes.

    导出格式已修改为RuleDataSet格式，确保与子节点期望的格式完全匹配。

    Args:
        request: HTTP request object

    Returns:
        Response: Gzipped JSON package containing all active rules in RuleDataSet format
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Starting rules export: {request_id}")

        # Add request tracking event
        if request_id:
            request_tracker.add_event(
                request_id,
                "export_request",
                {"endpoint": "/api/v1/rules/export", "operation": "export_rules"},
            )

        # 从数据库加载规则数据
        from core.db_session import get_session_factory
        from services.rule_loader import load_rules_from_db

        session_factory = get_session_factory()
        with session_factory() as session:
            rules_by_key = load_rules_from_db(session)

        if not rules_by_key:
            logger.warning(f"No rules to export: {request_id}")
            raise HTTPException(status_code=ErrorCodes.RULE_NOT_FOUND, detail="No rules available for export")

        # 计算版本哈希（基于规则键）
        rule_keys = sorted(rules_by_key.keys())
        version_string = "".join(rule_keys)
        version_hash = hashlib.sha256(version_string.encode("utf-8")).hexdigest()

        # 将规则数据转换为导出格式（原始rule_datasets格式）
        exported_rule_datasets = []
        for rule_key, rule_data_list in rules_by_key.items():
            try:
                # 为每个规则键创建一个数据集条目
                dataset_entry = {
                    "rule_key": rule_key,
                    "rule_data": rule_data_list,
                    "count": len(rule_data_list),
                    "version": version_hash[:8],
                }
                exported_rule_datasets.append(dataset_entry)
            except Exception as e:
                logger.error(f"Failed to serialize rule dataset {rule_key}: {e}")
                continue

        # 创建导出包，使用原始的rule_datasets格式
        package = {
            "version": version_hash,
            "rule_datasets": exported_rule_datasets,
            "export_timestamp": datetime.now().isoformat(),
            "total_count": len(exported_rule_datasets),
        }

        json_data = json.dumps(package, ensure_ascii=False, indent=None)
        gzipped_data = gzip.compress(json_data.encode("utf-8"))

        logger.info(
            f"Rules export completed: {request_id} - {len(exported_rule_datasets)} rule datasets, "
            f"{len(gzipped_data)} bytes"
        )

        # Add completion event
        if request_id:
            request_tracker.add_event(
                request_id,
                "export_completed",
                {
                    "rule_datasets_count": len(exported_rule_datasets),
                    "package_size": len(gzipped_data),
                    "version_hash": version_hash[:8],
                },
            )
            request_tracker.complete_request(request_id, success=True)
        return Response(
            content=gzipped_data,
            media_type="application/json",
            headers={
                "Content-Encoding": "gzip",
                "X-Request-ID": request_id or "unknown",
                "X-Rules-Count": str(len(exported_rule_datasets)),
                "X-Package-Version": version_hash[:8],
                "X-Export-Format": "RuleDataSet",
            },
        )

    except Exception as e:
        logger.error(f"Error exporting rules: {request_id} - {str(e)}", exc_info=True)

        # Add error event
        if request_id:
            request_tracker.add_event(
                request_id,
                "export_error",
                {"error_type": type(e).__name__, "error_message": str(e)},
            )
            request_tracker.complete_request(request_id, success=False)

        # Re-raise to let unified error handler process it
        raise
