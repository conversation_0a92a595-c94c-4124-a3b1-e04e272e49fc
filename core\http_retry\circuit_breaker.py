"""
智能断路器模式实现
提供滑动窗口统计、智能半开状态恢复和成功率监控功能
"""

import logging
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """断路器状态枚举"""

    CLOSED = "closed"        # 关闭状态：正常处理请求
    OPEN = "open"           # 打开状态：拒绝所有请求
    HALF_OPEN = "half_open" # 半开状态：允许少量请求测试服务恢复


@dataclass
class CircuitBreakerConfig:
    """智能断路器配置"""

    # 基础配置
    failure_threshold: int = 5                    # 失败阈值(次数)
    failure_rate_threshold: float = 0.5          # 失败率阈值(0.0-1.0)
    recovery_timeout: float = 60.0               # 恢复超时(秒)

    # 滑动窗口配置
    window_size: int = 100                       # 滑动窗口大小
    min_requests_threshold: int = 10             # 最小请求数阈值

    # 半开状态配置
    half_open_max_calls: int = 3                 # 半开状态最大调用数
    half_open_success_threshold: float = 0.6     # 半开状态成功率阈值

    # 监控配置
    enable_metrics: bool = True                  # 启用指标收集
    metrics_window_size: int = 1000              # 指标窗口大小

    def __post_init__(self):
        """配置验证"""
        if self.failure_threshold < 1:
            raise ValueError("failure_threshold必须大于等于1")
        if not (0.0 <= self.failure_rate_threshold <= 1.0):
            raise ValueError("failure_rate_threshold必须在0.0到1.0之间")
        if self.recovery_timeout <= 0:
            raise ValueError("recovery_timeout必须大于0")
        if self.window_size < 1:
            raise ValueError("window_size必须大于等于1")
        if self.half_open_max_calls < 1:
            raise ValueError("half_open_max_calls必须大于等于1")


@dataclass
class RequestRecord:
    """请求记录"""

    timestamp: float
    success: bool
    response_time: float = 0.0
    error_type: str | None = None


@dataclass
class CircuitBreakerMetrics:
    """断路器指标"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0

    # 状态转换统计
    state_transitions: dict[str, int] = field(default_factory=dict)

    # 时间统计
    total_response_time: float = 0.0
    last_failure_time: float = 0.0
    last_success_time: float = 0.0

    # 当前状态
    current_state: CircuitBreakerState = CircuitBreakerState.CLOSED
    state_change_time: float = field(default_factory=time.time)

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests

    def get_failure_rate(self) -> float:
        """获取失败率"""
        return 1.0 - self.get_success_rate()

    def get_average_response_time(self) -> float:
        """获取平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests


class SmartCircuitBreaker:
    """智能断路器实现"""

    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.last_failure_time = 0.0
        self.state_change_time = time.time()

        # 滑动窗口
        self.request_window = deque(maxlen=config.window_size)

        # 半开状态计数
        self.half_open_calls = 0
        self.half_open_successes = 0

        # 指标收集
        self.metrics = CircuitBreakerMetrics()

        # 线程安全锁
        self._lock = threading.RLock()

        logger.info(f"智能断路器 '{name}' 初始化完成")

    def can_execute(self) -> bool:
        """判断是否可以执行请求"""
        with self._lock:
            current_time = time.time()

            if self.state == CircuitBreakerState.CLOSED:
                return True
            elif self.state == CircuitBreakerState.OPEN:
                # 检查是否可以进入半开状态
                if current_time - self.last_failure_time >= self.config.recovery_timeout:
                    self._transition_to_half_open()
                    return True
                return False
            else:  # HALF_OPEN
                # 半开状态下限制请求数量
                return self.half_open_calls < self.config.half_open_max_calls

    def record_success(self, response_time: float = 0.0):
        """记录成功请求"""
        with self._lock:
            current_time = time.time()

            # 记录到滑动窗口
            record = RequestRecord(
                timestamp=current_time,
                success=True,
                response_time=response_time
            )
            self.request_window.append(record)

            # 更新指标
            self._update_metrics(record)

            # 状态处理
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.half_open_calls += 1
                self.half_open_successes += 1

                # 检查是否可以关闭断路器
                if self.half_open_calls >= self.config.half_open_max_calls:
                    success_rate = self.half_open_successes / self.half_open_calls
                    if success_rate >= self.config.half_open_success_threshold:
                        self._transition_to_closed()
                    else:
                        self._transition_to_open()

            logger.debug(f"断路器 '{self.name}' 记录成功请求，响应时间: {response_time:.3f}s")

    def record_failure(self, error_type: str = None):
        """记录失败请求"""
        with self._lock:
            current_time = time.time()

            # 记录到滑动窗口
            record = RequestRecord(
                timestamp=current_time,
                success=False,
                error_type=error_type
            )
            self.request_window.append(record)

            # 更新指标
            self._update_metrics(record)

            # 更新失败时间
            self.last_failure_time = current_time

            # 状态处理
            if self.state == CircuitBreakerState.CLOSED:
                # 检查是否需要打开断路器
                if self._should_open_circuit():
                    self._transition_to_open()
            elif self.state == CircuitBreakerState.HALF_OPEN:
                self.half_open_calls += 1
                # 半开状态下的失败直接打开断路器
                self._transition_to_open()

            logger.debug(f"断路器 '{self.name}' 记录失败请求，错误类型: {error_type}")

    def _should_open_circuit(self) -> bool:
        """判断是否应该打开断路器"""
        # 检查请求数量是否足够
        if len(self.request_window) < self.config.min_requests_threshold:
            return False

        # 计算窗口内的失败统计
        recent_requests = list(self.request_window)
        total_requests = len(recent_requests)
        failed_requests = sum(1 for r in recent_requests if not r.success)

        # 检查失败次数阈值
        if failed_requests >= self.config.failure_threshold:
            return True

        # 检查失败率阈值
        failure_rate = failed_requests / total_requests
        if failure_rate >= self.config.failure_rate_threshold:
            return True

        return False

    def _transition_to_open(self):
        """转换到打开状态"""
        if self.state != CircuitBreakerState.OPEN:
            old_state = self.state
            self.state = CircuitBreakerState.OPEN
            self.state_change_time = time.time()
            self._record_state_transition(old_state, CircuitBreakerState.OPEN)

            logger.warning(f"断路器 '{self.name}' 打开，失败率过高")

    def _transition_to_half_open(self):
        """转换到半开状态"""
        if self.state != CircuitBreakerState.HALF_OPEN:
            old_state = self.state
            self.state = CircuitBreakerState.HALF_OPEN
            self.state_change_time = time.time()
            self.half_open_calls = 0
            self.half_open_successes = 0
            self._record_state_transition(old_state, CircuitBreakerState.HALF_OPEN)

            logger.info(f"断路器 '{self.name}' 进入半开状态，开始测试服务恢复")

    def _transition_to_closed(self):
        """转换到关闭状态"""
        if self.state != CircuitBreakerState.CLOSED:
            old_state = self.state
            self.state = CircuitBreakerState.CLOSED
            self.state_change_time = time.time()
            self._record_state_transition(old_state, CircuitBreakerState.CLOSED)

            logger.info(f"断路器 '{self.name}' 关闭，服务已恢复")

    def _record_state_transition(self, from_state: CircuitBreakerState, to_state: CircuitBreakerState):
        """记录状态转换"""
        transition_key = f"{from_state.value}_to_{to_state.value}"
        self.metrics.state_transitions[transition_key] = (
            self.metrics.state_transitions.get(transition_key, 0) + 1
        )
        self.metrics.current_state = to_state
        self.metrics.state_change_time = time.time()

    def _update_metrics(self, record: RequestRecord):
        """更新指标"""
        if not self.config.enable_metrics:
            return

        self.metrics.total_requests += 1

        if record.success:
            self.metrics.successful_requests += 1
            self.metrics.total_response_time += record.response_time
            self.metrics.last_success_time = record.timestamp
        else:
            self.metrics.failed_requests += 1
            self.metrics.last_failure_time = record.timestamp

    def get_status(self) -> dict[str, Any]:
        """获取断路器状态信息"""
        with self._lock:
            current_time = time.time()

            # 计算窗口统计
            recent_requests = list(self.request_window)
            window_total = len(recent_requests)
            window_failures = sum(1 for r in recent_requests if not r.success)
            window_success_rate = (window_total - window_failures) / max(window_total, 1)

            return {
                "name": self.name,
                "state": self.state.value,
                "state_duration": current_time - self.state_change_time,
                "window_size": window_total,
                "window_success_rate": window_success_rate,
                "window_failure_count": window_failures,
                "half_open_calls": self.half_open_calls,
                "half_open_successes": self.half_open_successes,
                "metrics": {
                    "total_requests": self.metrics.total_requests,
                    "success_rate": self.metrics.get_success_rate(),
                    "failure_rate": self.metrics.get_failure_rate(),
                    "average_response_time": self.metrics.get_average_response_time(),
                    "state_transitions": self.metrics.state_transitions
                }
            }

    def reset(self):
        """重置断路器状态"""
        with self._lock:
            self.state = CircuitBreakerState.CLOSED
            self.last_failure_time = 0.0
            self.state_change_time = time.time()
            self.request_window.clear()
            self.half_open_calls = 0
            self.half_open_successes = 0
            self.metrics = CircuitBreakerMetrics()

            logger.info(f"断路器 '{self.name}' 已重置")


class CircuitBreakerManager:
    """断路器管理器"""

    def __init__(self):
        self.circuit_breakers: dict[str, SmartCircuitBreaker] = {}
        self._lock = threading.RLock()

    def get_circuit_breaker(self, name: str, config: CircuitBreakerConfig = None) -> SmartCircuitBreaker:
        """获取或创建断路器"""
        with self._lock:
            if name not in self.circuit_breakers:
                if config is None:
                    config = CircuitBreakerConfig()
                self.circuit_breakers[name] = SmartCircuitBreaker(name, config)

            return self.circuit_breakers[name]

    def remove_circuit_breaker(self, name: str):
        """移除断路器"""
        with self._lock:
            if name in self.circuit_breakers:
                del self.circuit_breakers[name]
                logger.info(f"断路器 '{name}' 已移除")

    def get_all_status(self) -> dict[str, dict[str, Any]]:
        """获取所有断路器状态"""
        with self._lock:
            return {name: cb.get_status() for name, cb in self.circuit_breakers.items()}

    def reset_all(self):
        """重置所有断路器"""
        with self._lock:
            for cb in self.circuit_breakers.values():
                cb.reset()
            logger.info("所有断路器已重置")


# 全局断路器管理器实例
_global_circuit_breaker_manager = CircuitBreakerManager()


def get_circuit_breaker_manager() -> CircuitBreakerManager:
    """获取全局断路器管理器"""
    return _global_circuit_breaker_manager
