import time
from concurrent.futures import ProcessPoolExecutor, as_completed

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE
from models.patient import PatientData
from models.rule import RuleResult
from rules.base_rules.base import BaseRule


def _execute_rule(rule: BaseRule, patient_data: PatientData) -> RuleResult | None:
    """
    一个独立的、可被序列化的顶层函数，用于在子进程中执行单个规则的校验。

    Args:
        rule (BaseRule): 要执行的规则实例。
        patient_data (dict[str, Any]): 患者数据。

    Returns:
        ValidationResult | None: 如果规则被触发，则返回结果；否则返回 None。
    """
    try:
        # 注意：BaseRule 的 validate 方法是同步的
        result = rule.validate(patient_data)
        return result
    except Exception as e:
        logger.error(f"Error executing rule '{rule.rule_id}' in a subprocess: {e}", exc_info=True)
        # 在子进程中发生错误，我们将其封装成一个结果返回，以便主进程知晓
        return RuleResult(
            id=rule.rule_id,
            output=None,
        )


class ValidationService:
    """
    一个高性能的规则校验服务，使用多进程并行处理规则。
    """

    def __init__(self):
        logger.info("High-Performance ValidationService initialized.")

    def validate_patient_data(
        self,
        patient_data: PatientData,
        rule_ids: list[str],
    ) -> list[RuleResult]:
        """
        使用进程池并行校验指定的规则列表。

        Args:
            patient_data (dict[str, Any]): 患者数据。
            rule_ids (list[str]): 需要校验的规则ID列表。

        Returns:
            list[ValidationResult]: 触发的规则结果列表。
        """
        start_time = time.time()
        violations = []
        rules_to_validate: list[BaseRule] = []

        # 1. 从缓存中安全地获取需要校验的规则实例
        for rule_id in rule_ids:
            rule_instance = RULE_CACHE.get(rule_id)
            if rule_instance:
                rules_to_validate.append(rule_instance)
            else:
                logger.warning(f"Rule with ID '{rule_id}' not found in cache. Skipping.")

        if not rules_to_validate:
            logger.info("No valid rules found to validate.")
            return []

        # 2. 使用 ProcessPoolExecutor 并行执行校验
        with ProcessPoolExecutor() as executor:
            # 提交所有任务
            future_to_rule = {executor.submit(_execute_rule, rule, patient_data): rule for rule in rules_to_validate}

            # 收集已完成任务的结果
            for future in as_completed(future_to_rule):
                rule = future_to_rule[future]
                try:
                    result = future.result()
                    if result:  # 只有被触发的规则才会返回非 None 的结果
                        violations.append(result)
                except Exception as exc:
                    logger.error(f"Rule '{rule.rule_id}' generated an exception in future: {exc}", exc_info=True)
                    violations.append(
                        RuleResult(
                            id=rule.rule_id,
                            output=f"Future Execution Error: {str(exc)}",
                        )
                    )

        total_time = time.time() - start_time
        logger.info(
            f"Validated {len(rules_to_validate)} rules in parallel, found {len(violations)} violations. "
            f"Total time: {total_time:.4f}s",
        )

        return violations
