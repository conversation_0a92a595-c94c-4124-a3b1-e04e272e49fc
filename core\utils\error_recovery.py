"""
错误恢复机制
为不同类型的错误提供恢复策略，确保系统的健壮性
"""

import asyncio
import time
from collections.abc import Callable
from dataclasses import dataclass
from enum import Enum
from typing import Any

from core.constants.error_codes import ErrorCodes
from core.constants.error_messages import ErrorMessages
from core.logging.logging_system import log as logger
from models.api import ApiResponse

# 导入新的指数退避算法和智能断路器
try:
    from config.retry_config import get_retry_config_manager
    from core.http_retry.backoff import BackoffStrategy, calculate_backoff_delay
    from core.http_retry.circuit_breaker import CircuitBreakerConfig, get_circuit_breaker_manager

    ENHANCED_RETRY_AVAILABLE = True
except ImportError:
    # 向后兼容：如果新模块不可用，使用原有逻辑
    ENHANCED_RETRY_AVAILABLE = False
    logger.warning("增强重试模块不可用，使用原有重试逻辑")


class RecoveryStrategy(Enum):
    """恢复策略枚举"""

    RETRY = "retry"  # 重试
    FALLBACK = "fallback"  # 降级处理
    CIRCUIT_BREAKER = "circuit_breaker"  # 熔断
    GRACEFUL_DEGRADATION = "graceful_degradation"  # 优雅降级
    FAIL_FAST = "fail_fast"  # 快速失败

    # 性能降级相关策略
    PERFORMANCE_DEGRADATION = "performance_degradation"  # 性能降级
    RESOURCE_THROTTLING = "resource_throttling"  # 资源限流
    COMPONENT_ISOLATION = "component_isolation"  # 组件隔离


@dataclass
class RecoveryConfig:
    """恢复配置"""

    strategy: RecoveryStrategy
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0
    fallback_enabled: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0


class ErrorRecoveryManager:
    """
    错误恢复管理器
    为不同类型的错误提供恢复策略
    """

    def __init__(self):
        """初始化错误恢复管理器"""
        self.recovery_configs = self._init_recovery_configs()
        self.circuit_breakers = {}
        self.retry_counts = {}

        # 初始化增强重试配置管理器
        self.retry_config_manager = None
        self.smart_circuit_breaker_manager = None

        if ENHANCED_RETRY_AVAILABLE:
            try:
                self.retry_config_manager = get_retry_config_manager()
                self.smart_circuit_breaker_manager = get_circuit_breaker_manager()
                logger.info("增强重试和断路器管理器初始化成功")
            except Exception as e:
                logger.warning(f"增强模块初始化失败: {e}")
                self.retry_config_manager = None
                self.smart_circuit_breaker_manager = None

    def _init_recovery_configs(self) -> dict[int, RecoveryConfig]:
        """
        初始化恢复配置

        Returns:
            Dict: 错误码到恢复配置的映射
        """
        return {
            # 网络错误 - 重试策略
            ErrorCodes.NETWORK_ERROR: RecoveryConfig(
                strategy=RecoveryStrategy.RETRY, max_retries=3, retry_delay=2.0, timeout=30.0
            ),
            # 数据库错误 - 重试 + 熔断
            ErrorCodes.DATABASE_ERROR: RecoveryConfig(
                strategy=RecoveryStrategy.CIRCUIT_BREAKER,
                max_retries=2,
                retry_delay=1.0,
                circuit_breaker_threshold=5,
                circuit_breaker_timeout=60.0,
            ),
            # 缓存错误 - 降级处理
            ErrorCodes.CACHE_ERROR: RecoveryConfig(
                strategy=RecoveryStrategy.GRACEFUL_DEGRADATION, max_retries=1, fallback_enabled=True
            ),
            # 规则同步失败 - 重试 + 降级
            ErrorCodes.RULE_SYNC_FAILED: RecoveryConfig(
                strategy=RecoveryStrategy.RETRY, max_retries=5, retry_delay=5.0, fallback_enabled=True
            ),
            # 规则执行错误 - 快速失败
            ErrorCodes.RULE_EXECUTION_ERROR: RecoveryConfig(strategy=RecoveryStrategy.FAIL_FAST, max_retries=0),
            # 服务不可用 - 熔断
            ErrorCodes.SERVICE_UNAVAILABLE: RecoveryConfig(
                strategy=RecoveryStrategy.CIRCUIT_BREAKER, circuit_breaker_threshold=3, circuit_breaker_timeout=120.0
            ),
            # 内存错误 - 优雅降级
            ErrorCodes.MEMORY_ERROR: RecoveryConfig(
                strategy=RecoveryStrategy.GRACEFUL_DEGRADATION, max_retries=0, fallback_enabled=True
            ),
            # 验证错误 - 快速失败
            ErrorCodes.VALIDATION_ERROR: RecoveryConfig(strategy=RecoveryStrategy.FAIL_FAST, max_retries=0),
        }

    def _is_retryable_error(self, error: Exception, error_code: int) -> bool:
        """
        判断错误是否可重试

        Args:
            error: 异常对象
            error_code: 错误码

        Returns:
            bool: 是否可重试
        """
        # 如果有增强重试配置，使用新的错误分类逻辑
        if self.retry_config_manager:
            try:
                retry_config = self.retry_config_manager.get_retry_config()

                # 检查HTTP状态码（如果错误包含状态码信息）
                if hasattr(error, "status_code"):
                    status_code = error.status_code
                    # 确保retryable_status_codes不为None
                    retryable_codes = retry_config.retryable_status_codes or []
                    if status_code in retryable_codes:
                        return True
                    # 4xx错误（除了408, 429）通常不可重试
                    if 400 <= status_code < 500 and status_code not in [408, 429]:
                        return False

                # 检查异常类型
                error_type = type(error).__name__
                if "ConnectionError" in error_type and retry_config.retry_on_connection_error:
                    return True
                if "TimeoutError" in error_type and retry_config.retry_on_timeout_error:
                    return True
                if "DNSError" in error_type and retry_config.retry_on_dns_error:
                    return True

            except Exception as e:
                logger.warning(f"增强错误分类失败，使用默认逻辑: {e}")

        # 默认的可重试错误码
        retryable_error_codes = {
            ErrorCodes.NETWORK_ERROR,
            ErrorCodes.TIMEOUT_ERROR,
            ErrorCodes.SERVICE_UNAVAILABLE,
            ErrorCodes.INTERNAL_SERVER_ERROR,
            ErrorCodes.DATABASE_CONNECTION_ERROR,
            ErrorCodes.EXTERNAL_API_ERROR,
        }

        return error_code in retryable_error_codes

    def _calculate_retry_delay(self, attempt: int, config: RecoveryConfig) -> float:
        """
        计算重试延迟时间

        Args:
            attempt: 重试次数(从0开始)
            config: 恢复配置

        Returns:
            float: 延迟时间(秒)
        """
        # 如果有增强重试配置，使用指数退避算法
        if self.retry_config_manager and ENHANCED_RETRY_AVAILABLE:
            try:
                retry_config = self.retry_config_manager.get_retry_config()
                if retry_config.enabled:
                    delay = calculate_backoff_delay(
                        attempt=attempt,
                        base_delay=retry_config.base_delay,
                        max_delay=retry_config.max_delay,
                        backoff_factor=retry_config.backoff_factor,
                        jitter=retry_config.jitter,
                        strategy=BackoffStrategy.EXPONENTIAL,
                    )
                    return delay
            except Exception as e:
                logger.warning(f"增强重试延迟计算失败，使用默认逻辑: {e}")

        # 向后兼容：使用原有的线性延迟逻辑
        return config.retry_delay * (attempt + 1)

    def _get_circuit_breaker_config(self, config: RecoveryConfig) -> "CircuitBreakerConfig":
        """
        获取断路器配置

        Args:
            config: 恢复配置

        Returns:
            CircuitBreakerConfig: 断路器配置
        """
        # 如果有增强配置管理器，使用增强配置并转换为智能断路器配置
        if self.retry_config_manager and ENHANCED_RETRY_AVAILABLE:
            try:
                retry_circuit_config = self.retry_config_manager.get_circuit_breaker_config()
                # 将config.retry_config.CircuitBreakerConfig转换为core.http_retry.circuit_breaker.CircuitBreakerConfig
                return CircuitBreakerConfig(
                    failure_threshold=retry_circuit_config.failure_threshold,
                    failure_rate_threshold=retry_circuit_config.failure_rate_threshold,
                    recovery_timeout=retry_circuit_config.recovery_timeout,
                    window_size=retry_circuit_config.window_size,
                    min_requests_threshold=10,  # 智能断路器特有参数，使用默认值
                    half_open_max_calls=retry_circuit_config.half_open_max_calls,
                    enable_metrics=True,  # 智能断路器特有参数，启用指标收集
                )
            except Exception as e:
                logger.warning(f"获取增强断路器配置失败: {e}")

        # 向后兼容：从RecoveryConfig创建断路器配置
        if ENHANCED_RETRY_AVAILABLE:
            try:
                return CircuitBreakerConfig(
                    failure_threshold=config.circuit_breaker_threshold,
                    recovery_timeout=config.circuit_breaker_timeout,
                    failure_rate_threshold=0.5,  # 默认失败率阈值
                    window_size=100,  # 默认窗口大小
                    min_requests_threshold=10,  # 默认最小请求数
                    half_open_max_calls=3,  # 默认半开状态最大调用数
                    enable_metrics=True,  # 启用指标收集
                )
            except Exception as e:
                logger.warning(f"创建断路器配置失败: {e}")
                # 如果创建失败，返回None，会回退到传统断路器
                return None

        return None

    async def handle_error_with_recovery(
        self, error: Exception, error_code: int, context: dict[str, Any], operation: Callable | None = None
    ) -> ApiResponse:
        """
        使用恢复策略处理错误

        Args:
            error: 原始异常
            error_code: 错误码
            context: 错误上下文
            operation: 可重试的操作函数

        Returns:
            ApiResponse: 处理后的响应
        """
        request_id = context.get("request_id")

        # 获取恢复配置
        config = self.recovery_configs.get(error_code)
        if not config:
            # 没有特定配置，使用默认策略
            return self._create_default_error_response(error_code, str(error), request_id)

        logger.info(
            f"Applying recovery strategy: {config.strategy.value} for error {error_code}",
            extra={"request_id": request_id, "error_code": error_code, "strategy": config.strategy.value},
        )

        try:
            if config.strategy == RecoveryStrategy.RETRY and operation:
                return await self._handle_retry_strategy(error, error_code, context, operation, config)
            elif config.strategy == RecoveryStrategy.CIRCUIT_BREAKER:
                return await self._handle_circuit_breaker_strategy(error, error_code, context, operation, config)
            elif config.strategy == RecoveryStrategy.GRACEFUL_DEGRADATION:
                return await self._handle_graceful_degradation_strategy(error, error_code, context, config)
            elif config.strategy == RecoveryStrategy.FALLBACK:
                return await self._handle_fallback_strategy(error, error_code, context, config)
            else:  # FAIL_FAST
                return await self._handle_fail_fast_strategy(error, error_code, context)

        except Exception as recovery_error:
            logger.error(
                f"Error in recovery strategy: {str(recovery_error)}",
                extra={"request_id": request_id, "original_error": str(error)},
                exc_info=True,
            )
            return self._create_default_error_response(error_code, str(error), request_id)

    async def _handle_retry_strategy(
        self, error: Exception, error_code: int, context: dict[str, Any], operation: Callable, config: RecoveryConfig
    ) -> ApiResponse:
        """处理重试策略（增强版）"""
        request_id = context.get("request_id")
        retry_key = f"{request_id}:{error_code}"

        # 获取当前重试次数
        current_retries = self.retry_counts.get(retry_key, 0)

        # 检查错误是否可重试
        if not self._is_retryable_error(error, error_code):
            logger.info(
                f"错误不可重试: {error_code} - {type(error).__name__}",
                extra={"request_id": request_id, "error_type": type(error).__name__},
            )
            return self._create_default_error_response(error_code, str(error), request_id)

        # 检查是否超过最大重试次数
        max_retries = config.max_retries
        # 如果有增强配置，使用增强配置的重试次数
        if self.retry_config_manager:
            try:
                retry_config = self.retry_config_manager.get_retry_config()
                if retry_config.enabled:
                    max_retries = retry_config.max_attempts
            except Exception as e:
                logger.warning(f"获取增强重试配置失败: {e}")

        if current_retries >= max_retries:
            logger.warning(
                f"超过最大重试次数 {retry_key}",
                extra={
                    "request_id": request_id,
                    "retries": current_retries,
                    "max_retries": max_retries,
                    "error_type": type(error).__name__,
                },
            )
            # 清除重试计数
            if retry_key in self.retry_counts:
                del self.retry_counts[retry_key]

            # 如果启用了降级，尝试降级处理
            if config.fallback_enabled:
                return await self._handle_graceful_degradation_strategy(error, error_code, context, config)
            else:
                return self._create_default_error_response(error_code, str(error), request_id)

        # 增加重试次数
        self.retry_counts[retry_key] = current_retries + 1

        # 计算重试延迟（使用增强的指数退避算法）
        delay = self._calculate_retry_delay(current_retries, config)

        # 记录详细的重试信息
        logger.info(
            f"准备重试操作 (第 {current_retries + 1}/{max_retries} 次)",
            extra={
                "request_id": request_id,
                "error_code": error_code,
                "error_type": type(error).__name__,
                "retry_delay": delay,
                "retry_attempt": current_retries + 1,
                "max_retries": max_retries,
                "enhanced_retry": self.retry_config_manager is not None,
            },
        )

        # 等待重试延迟
        await asyncio.sleep(delay)

        try:
            # 执行重试操作
            result = await operation() if asyncio.iscoroutinefunction(operation) else operation()

            # 重试成功，记录成功信息并清除重试计数
            logger.info(
                f"重试操作成功 (第 {current_retries + 1} 次尝试)",
                extra={
                    "request_id": request_id,
                    "error_code": error_code,
                    "retry_attempt": current_retries + 1,
                    "total_delay": delay,
                    "success_after_retries": True,
                },
            )

            if retry_key in self.retry_counts:
                del self.retry_counts[retry_key]

            return result

        except Exception as retry_error:
            # 记录重试失败信息
            logger.warning(
                f"重试操作失败: {str(retry_error)}",
                extra={
                    "request_id": request_id,
                    "retry_attempt": current_retries + 1,
                    "max_retries": max_retries,
                    "error_type": type(retry_error).__name__,
                    "original_error": str(error),
                    "retry_error": str(retry_error),
                },
            )

            # 递归调用，继续重试或最终失败
            return await self._handle_retry_strategy(retry_error, error_code, context, operation, config)

    async def _handle_circuit_breaker_strategy(
        self,
        error: Exception,
        error_code: int,
        context: dict[str, Any],
        operation: Callable | None,
        config: RecoveryConfig,
    ) -> ApiResponse:
        """处理断路器策略（增强版）"""
        # request_id = context.get("request_id")
        circuit_key = f"circuit:{error_code}"

        # 如果有智能断路器管理器，使用增强断路器
        if self.smart_circuit_breaker_manager and ENHANCED_RETRY_AVAILABLE:
            return await self._handle_smart_circuit_breaker(error, error_code, context, operation, config, circuit_key)
        else:
            # 向后兼容：使用原有断路器逻辑
            return await self._handle_legacy_circuit_breaker(error, error_code, context, operation, config, circuit_key)

    async def _handle_smart_circuit_breaker(
        self,
        error: Exception,
        error_code: int,
        context: dict[str, Any],
        operation: Callable | None,
        config: RecoveryConfig,
        circuit_key: str,
    ) -> ApiResponse:
        """处理智能断路器策略"""
        request_id = context.get("request_id")

        try:
            # 获取断路器配置
            circuit_config = self._get_circuit_breaker_config(config)

            # 获取智能断路器实例
            smart_breaker = self.smart_circuit_breaker_manager.get_circuit_breaker(circuit_key, circuit_config)

            # 检查是否可以执行请求
            if not smart_breaker.can_execute():
                logger.warning(
                    f"智能断路器 '{circuit_key}' 阻止请求执行",
                    extra={
                        "request_id": request_id,
                        "error_code": error_code,
                        "circuit_state": smart_breaker.state.value,
                    },
                )
                return self._create_circuit_breaker_response(error_code, request_id)

            # 如果没有操作函数，直接记录失败
            if not operation:
                smart_breaker.record_failure(type(error).__name__)
                return self._create_default_error_response(error_code, str(error), request_id)

            # 执行操作并记录结果
            start_time = time.time()
            try:
                result = await operation() if asyncio.iscoroutinefunction(operation) else operation()

                # 记录成功
                response_time = time.time() - start_time
                smart_breaker.record_success(response_time)

                logger.info(
                    "断路器保护下的操作成功",
                    extra={
                        "request_id": request_id,
                        "circuit_key": circuit_key,
                        "response_time": response_time,
                        "circuit_state": smart_breaker.state.value,
                    },
                )

                return result

            except Exception as operation_error:
                # 记录失败
                smart_breaker.record_failure(type(operation_error).__name__)

                logger.warning(
                    f"断路器保护下的操作失败: {str(operation_error)}",
                    extra={
                        "request_id": request_id,
                        "circuit_key": circuit_key,
                        "error_type": type(operation_error).__name__,
                        "circuit_state": smart_breaker.state.value,
                    },
                )

                # 根据断路器状态决定后续处理
                if smart_breaker.state.value == "open":
                    return self._create_circuit_breaker_response(error_code, request_id)
                else:
                    return self._create_default_error_response(error_code, str(operation_error), request_id)

        except Exception as e:
            logger.error(f"智能断路器处理失败: {e}", exc_info=True)
            # 回退到原有逻辑
            return await self._handle_legacy_circuit_breaker(error, error_code, context, operation, config, circuit_key)

    async def _handle_legacy_circuit_breaker(
        self,
        error: Exception,
        error_code: int,
        context: dict[str, Any],
        operation: Callable | None,
        config: RecoveryConfig,
        circuit_key: str,
    ) -> ApiResponse:
        """处理传统断路器策略（向后兼容）"""
        request_id = context.get("request_id")

        # 检查熔断器状态
        circuit_breaker = self.circuit_breakers.get(circuit_key, {"failures": 0, "last_failure": 0, "state": "closed"})

        current_time = time.time()

        # 如果熔断器打开且还在超时期内
        if (
            circuit_breaker["state"] == "open"
            and current_time - circuit_breaker["last_failure"] < config.circuit_breaker_timeout
        ):
            logger.warning(f"传统断路器打开 {circuit_key}", extra={"request_id": request_id, "error_code": error_code})
            return self._create_circuit_breaker_response(error_code, request_id)

        # 如果熔断器打开但超时期已过，尝试半开状态
        if circuit_breaker["state"] == "open":
            circuit_breaker["state"] = "half-open"
            logger.info(f"传统断路器进入半开状态 {circuit_key}")

        # 记录失败
        circuit_breaker["failures"] += 1
        circuit_breaker["last_failure"] = current_time

        # 检查是否需要打开熔断器
        if circuit_breaker["failures"] >= config.circuit_breaker_threshold:
            circuit_breaker["state"] = "open"
            logger.warning(
                f"传统断路器打开 {circuit_key}",
                extra={"request_id": request_id, "failures": circuit_breaker["failures"]},
            )

        self.circuit_breakers[circuit_key] = circuit_breaker

        # 如果有操作且熔断器未打开，尝试重试
        if operation and circuit_breaker["state"] != "open":
            try:
                result = await operation() if asyncio.iscoroutinefunction(operation) else operation()
                # 操作成功，重置熔断器
                circuit_breaker["failures"] = 0
                circuit_breaker["state"] = "closed"
                return result
            except Exception:
                pass

        return self._create_default_error_response(error_code, str(error), request_id)

    async def _handle_graceful_degradation_strategy(
        self, error: Exception, error_code: int, context: dict[str, Any], config: RecoveryConfig
    ) -> ApiResponse:
        """处理优雅降级策略"""
        request_id = context.get("request_id")

        logger.info(
            f"Applying graceful degradation for error {error_code}",
            extra={"request_id": request_id, "error_code": error_code},
        )

        # 根据错误类型提供降级服务
        if error_code == ErrorCodes.CACHE_ERROR:
            return self._create_cache_degradation_response(request_id)
        elif error_code == ErrorCodes.DATABASE_ERROR:
            return self._create_database_degradation_response(request_id)
        elif error_code == ErrorCodes.MEMORY_ERROR:
            return self._create_memory_degradation_response(request_id)
        else:
            return self._create_default_degradation_response(error_code, request_id)

    async def _handle_fallback_strategy(
        self, error: Exception, error_code: int, context: dict[str, Any], config: RecoveryConfig
    ) -> ApiResponse:
        """处理降级处理策略"""
        request_id = context.get("request_id")

        logger.info(
            f"Applying fallback strategy for error {error_code}",
            extra={"request_id": request_id, "error_code": error_code},
        )

        # 提供基本的降级服务
        return ApiResponse.error_response(
            code=error_code,
            message=f"{ErrorMessages.get_message(error_code)}（降级模式）",
            request_id=request_id,
            debug_info={"fallback_mode": True, "original_error": str(error)},
        )

    async def _handle_fail_fast_strategy(
        self, error: Exception, error_code: int, context: dict[str, Any]
    ) -> ApiResponse:
        """处理快速失败策略"""
        request_id = context.get("request_id")

        logger.info(
            f"Applying fail-fast strategy for error {error_code}",
            extra={"request_id": request_id, "error_code": error_code},
        )

        return self._create_default_error_response(error_code, str(error), request_id)

    def _create_default_error_response(
        self, error_code: int, error_message: str, request_id: str | None
    ) -> ApiResponse:
        """创建默认错误响应"""
        return ApiResponse.error_response(
            code=error_code,
            message=ErrorMessages.get_message(error_code),
            request_id=request_id,
            debug_info={"original_error": error_message},
        )

    def _create_circuit_breaker_response(self, error_code: int, request_id: str | None) -> ApiResponse:
        """创建熔断器响应"""
        return ApiResponse.error_response(
            code=ErrorCodes.SERVICE_UNAVAILABLE,
            message="服务暂时不可用，请稍后重试（熔断保护）",
            request_id=request_id,
            debug_info={"circuit_breaker": True, "original_error_code": error_code},
        )

    def _create_cache_degradation_response(self, request_id: str | None) -> ApiResponse:
        """创建缓存降级响应"""
        return ApiResponse.error_response(
            code=ErrorCodes.CACHE_ERROR,
            message="缓存服务异常，已切换到直接查询模式",
            request_id=request_id,
            debug_info={"degradation_mode": "direct_query"},
        )

    def _create_database_degradation_response(self, request_id: str | None) -> ApiResponse:
        """创建数据库降级响应"""
        return ApiResponse.error_response(
            code=ErrorCodes.DATABASE_ERROR,
            message="数据库服务异常，已启用只读模式",
            request_id=request_id,
            debug_info={"degradation_mode": "readonly"},
        )

    def _create_memory_degradation_response(self, request_id: str | None) -> ApiResponse:
        """创建内存降级响应"""
        return ApiResponse.error_response(
            code=ErrorCodes.MEMORY_ERROR,
            message="内存不足，已启用精简模式",
            request_id=request_id,
            debug_info={"degradation_mode": "minimal"},
        )

    def _create_default_degradation_response(self, error_code: int, request_id: str | None) -> ApiResponse:
        """创建默认降级响应"""
        return ApiResponse.error_response(
            code=error_code,
            message=f"{ErrorMessages.get_message(error_code)}（系统已启用降级模式）",
            request_id=request_id,
            debug_info={"degradation_mode": "default"},
        )

    def get_recovery_stats(self) -> dict[str, Any]:
        """获取恢复统计信息"""
        return {
            "circuit_breakers": {k: v for k, v in self.circuit_breakers.items()},
            "active_retries": len(self.retry_counts),
            "retry_counts": dict(self.retry_counts),
        }


# 全局错误恢复管理器实例
error_recovery_manager = ErrorRecoveryManager()
