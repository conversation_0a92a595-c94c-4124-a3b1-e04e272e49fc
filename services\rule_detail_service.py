"""
规则明细服务类 - 重构版本
提供规则明细的CRUD操作、数据验证、字段映射等功能
集成新的数据模型和UnifiedDataMappingEngine
支持元数据驱动的数据处理和验证
"""

import time
from functools import lru_cache
from typing import Any

from sqlalchemy import and_, desc, func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from core.logging.logging_system import log as logger
from models.database import RuleDetail, RuleDetailStatusEnum, RuleFieldMetadata, RuleTemplate
from services.unified_data_mapping_engine import UnifiedDataMappingEngine
from tools.field_mapping_manager import FieldMappingManager


class ServiceError(Exception):
    """服务层统一异常类"""

    def __init__(self, message: str, error_code: str = "SERVICE_ERROR", details: dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = time.time()


class ValidationResult:
    """验证结果类"""

    def __init__(self, valid: bool, errors: list[str] = None, warnings: list[str] = None, data: dict[str, Any] = None):
        self.valid = valid
        self.errors = errors or []
        self.warnings = warnings or []
        self.data = data or {}

    def to_dict(self) -> dict[str, Any]:
        return {"valid": self.valid, "errors": self.errors, "warnings": self.warnings, "data": self.data}


class RuleDetailService:
    """规则明细服务类 - 重构增强版本"""

    def __init__(self, session: Session):
        """
        初始化服务

        Args:
            session: 数据库会话
        """
        self.session = session
        self.field_mapping_manager = FieldMappingManager()
        self.data_mapping_engine = UnifiedDataMappingEngine()
        self._template_cache = {}  # 模板缓存
        self._metadata_cache = {}  # 元数据缓存

    def _log_operation(
        self,
        operation: str,
        rule_key: str = None,
        rule_id: str = None,
        duration: float = None,
        success: bool = True,
        error: str = None,
    ):
        """记录操作日志"""
        log_data = {
            "operation": operation,
            "rule_key": rule_key,
            "rule_id": rule_id,
            "duration_ms": round(duration * 1000, 2) if duration else None,
            "success": success,
            "error": error,
        }

        if success:
            logger.info(f"服务操作成功: {operation}", extra=log_data)
        else:
            logger.error(f"服务操作失败: {operation}", extra=log_data)

    def _handle_service_error(
        self, operation: str, error: Exception, rule_key: str = None, rule_id: str = None
    ) -> ServiceError:
        """统一错误处理"""
        error_msg = str(error)
        error_code = "DATABASE_ERROR" if isinstance(error, SQLAlchemyError) else "SERVICE_ERROR"

        details = {
            "operation": operation,
            "rule_key": rule_key,
            "rule_id": rule_id,
            "original_error": error_msg,
            "error_type": type(error).__name__,
        }

        service_error = ServiceError(
            message=f"操作 '{operation}' 执行失败: {error_msg}", error_code=error_code, details=details
        )

        self._log_operation(operation, rule_key, rule_id, success=False, error=error_msg)
        return service_error

    def create_rule_detail(self, rule_key: str, data: dict[str, Any]) -> RuleDetail:
        """
        创建规则明细 - 增强版本

        Args:
            rule_key: 规则模板键
            data: 规则明细数据

        Returns:
            RuleDetail: 创建的规则明细对象

        Raises:
            ServiceError: 当操作失败时
        """
        start_time = time.time()
        operation = "create_rule_detail"

        try:
            # 1. 验证输入参数
            self._validate_create_inputs(rule_key, data)

            # 2. 验证模板并获取元数据
            template, field_metadata_list = self._prepare_template_metadata(rule_key)

            # 3. 验证和处理数据
            validated_data = self._process_and_validate_data(data, rule_key, field_metadata_list)

            # 4. 创建规则明细对象
            rule_detail = self._create_rule_detail_object(validated_data)

            # 5. 保存到数据库
            self._save_rule_detail(rule_detail)

            # 6. 记录成功日志
            self._log_create_success(operation, rule_key, rule_detail, start_time)

            return rule_detail

        except ServiceError:
            raise
        except Exception as e:
            raise self._handle_service_error(operation, e, rule_key) from None

    def get_rule_detail(self, rule_key: str, rule_id: str) -> RuleDetail | None:
        """
        获取单条规则明细

        Args:
            rule_key: 规则模板键
            rule_id: 规则ID

        Returns:
            Optional[RuleDetail]: 规则明细对象，不存在时返回None
        """
        try:
            rule_detail = (
                self.session.query(RuleDetail)
                .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.rule_id == rule_id))
                .first()
            )

            if rule_detail:
                logger.debug(f"获取规则明细成功: rule_key={rule_key}, rule_id={rule_id}")
            else:
                logger.debug(f"规则明细不存在: rule_key={rule_key}, rule_id={rule_id}")

            return rule_detail

        except Exception as e:
            logger.error(f"获取规则明细失败: rule_key={rule_key}, rule_id={rule_id}, error={e}")
            raise

    def update_rule_detail(self, rule_key: str, rule_id: str, data: dict[str, Any]) -> RuleDetail:
        """
        更新规则明细

        Args:
            rule_key: 规则模板键
            rule_id: 规则ID
            data: 更新数据

        Returns:
            RuleDetail: 更新后的规则明细对象

        Raises:
            ValueError: 当规则明细不存在或数据验证失败时
        """
        try:
            # 1. 获取现有规则明细
            rule_detail = self.get_rule_detail(rule_key, rule_id)
            if not rule_detail:
                raise ServiceError(
                    f"规则明细不存在: rule_key={rule_key}, rule_id={rule_id}",
                    error_code="RULE_DETAIL_NOT_FOUND",
                    details={"rule_key": rule_key, "rule_id": rule_id},
                )

            # 2. 获取字段元数据
            template = self._get_template_by_key(rule_key)
            field_metadata_list = template.get_field_metadata_list() if template else []

            # 3. 数据标准化和验证
            normalized_data = self.data_mapping_engine.normalize_field_names(data)
            validation_result = self.data_mapping_engine.validate_data(normalized_data, rule_key)

            if not validation_result["valid"]:
                raise ServiceError(
                    f"数据验证失败: {', '.join(validation_result['errors'])}",
                    error_code="VALIDATION_FAILED",
                    details={"rule_key": rule_key, "rule_id": rule_id, "errors": validation_result["errors"]},
                )

            # 4. 合并数据到现有对象
            rule_detail.merge_from_dict(normalized_data, field_metadata_list)

            # 5. 使用字段元数据进行详细验证
            detail_validation = rule_detail.validate_data(field_metadata_list)
            if not detail_validation["valid"]:
                raise ServiceError(
                    f"详细验证失败: {', '.join(detail_validation['errors'])}",
                    error_code="DETAIL_VALIDATION_FAILED",
                    details={"rule_key": rule_key, "rule_id": rule_id, "errors": detail_validation["errors"]},
                )

            # 6. 保存更改
            self.session.flush()

            logger.info(f"成功更新规则明细: rule_key={rule_key}, rule_id={rule_id}")
            return rule_detail

        except Exception as e:
            logger.error(f"更新规则明细失败: rule_key={rule_key}, rule_id={rule_id}, error={e}")
            raise

    def delete_rule_detail(self, rule_key: str, rule_id: str) -> bool:
        """
        删除规则明细

        Args:
            rule_key: 规则模板键
            rule_id: 规则ID

        Returns:
            bool: 删除成功返回True，规则明细不存在返回False
        """
        try:
            rule_detail = self.get_rule_detail(rule_key, rule_id)
            if not rule_detail:
                logger.warning(f"尝试删除不存在的规则明细: rule_key={rule_key}, rule_id={rule_id}")
                return False

            self.session.delete(rule_detail)
            self.session.flush()

            logger.info(f"成功删除规则明细: rule_key={rule_key}, rule_id={rule_id}")
            return True

        except Exception as e:
            logger.error(f"删除规则明细失败: rule_key={rule_key}, rule_id={rule_id}, error={e}")
            raise

    def list_rule_details(
        self,
        rule_key: str,
        page: int = 1,
        page_size: int = 20,
        status: str | None = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        search: str | None = None,
        filters: dict[str, Any] | None = None,
    ) -> tuple[list[RuleDetail], int]:
        """
        查询规则明细列表

        Args:
            rule_key: 规则模板键
            page: 页码
            page_size: 每页大小
            status: 状态过滤
            sort_by: 排序字段
            sort_order: 排序方向
            search: 搜索关键词
            filters: 其他过滤条件

        Returns:
            Tuple[List[RuleDetail], int]: (规则明细列表, 总数)
        """
        try:
            # 构建查询
            query = self.session.query(RuleDetail).filter(RuleDetail.rule_key == rule_key)

            # 状态过滤
            if status:
                try:
                    status_enum = RuleDetailStatusEnum(status)
                    query = query.filter(RuleDetail.status == status_enum)
                except ValueError:
                    logger.warning(f"无效的状态值: {status}")

            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                query = query.filter(
                    RuleDetail.rule_name.like(search_pattern)
                    | RuleDetail.rule_id.like(search_pattern)
                    | RuleDetail.error_reason.like(search_pattern)
                )

            # 其他过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(RuleDetail, field) and value is not None:
                        query = query.filter(getattr(RuleDetail, field) == value)

            # 总数统计
            total = query.count()

            # 排序
            if hasattr(RuleDetail, sort_by):
                order_column = getattr(RuleDetail, sort_by)
                if sort_order.lower() == "desc":
                    query = query.order_by(desc(order_column))
                else:
                    query = query.order_by(order_column)

            # 分页
            offset = (page - 1) * page_size
            rule_details = query.offset(offset).limit(page_size).all()

            logger.debug(f"查询规则明细列表: rule_key={rule_key}, 返回{len(rule_details)}条记录，总计{total}条")
            return rule_details, total

        except Exception as e:
            logger.error(f"查询规则明细列表失败: rule_key={rule_key}, error={e}")
            raise

    def batch_create_rule_details(self, rule_key: str, data_list: list[dict[str, Any]]) -> list[RuleDetail]:
        """
        批量创建规则明细

        Args:
            rule_key: 规则模板键
            data_list: 规则明细数据列表

        Returns:
            List[RuleDetail]: 创建的规则明细对象列表

        Raises:
            ValueError: 当数据验证失败时
        """
        try:
            # 1. 验证规则模板是否存在
            template = self._get_template_by_key(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 不存在", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
                )

            field_metadata_list = template.get_field_metadata_list()
            created_details = []
            errors = []

            # 2. 批量处理数据
            for i, data in enumerate(data_list):
                try:
                    # 数据标准化和验证
                    normalized_data = self.data_mapping_engine.normalize_field_names(data)
                    validation_result = self.data_mapping_engine.validate_data(normalized_data, rule_key)

                    if not validation_result["valid"]:
                        errors.append(f"第{i+1}条数据验证失败: {', '.join(validation_result['errors'])}")
                        continue

                    # 分离固定字段和扩展字段
                    fixed_fields, extended_fields = self.data_mapping_engine.separate_fields(normalized_data)

                    # 创建规则明细对象
                    rule_detail = RuleDetail(**fixed_fields)
                    if extended_fields:
                        rule_detail.update_extended_fields(extended_fields)

                    # 详细验证
                    detail_validation = rule_detail.validate_data(field_metadata_list)
                    if not detail_validation["valid"]:
                        errors.append(f"第{i+1}条数据详细验证失败: {', '.join(detail_validation['errors'])}")
                        continue

                    created_details.append(rule_detail)

                except Exception as e:
                    errors.append(f"第{i+1}条数据处理失败: {str(e)}")

            # 3. 检查是否有错误
            if errors:
                raise ServiceError(
                    f"批量创建失败: {'; '.join(errors)}",
                    error_code="BATCH_CREATE_FAILED",
                    details={"rule_key": rule_key, "errors": errors, "failed_count": len(errors)},
                )

            # 4. 批量保存到数据库
            self.session.add_all(created_details)
            self.session.flush()

            logger.info(f"批量创建规则明细成功: rule_key={rule_key}, 创建{len(created_details)}条记录")
            return created_details

        except Exception as e:
            logger.error(f"批量创建规则明细失败: rule_key={rule_key}, error={e}")
            raise

    def batch_update_rule_details(self, rule_key: str, updates: list[dict[str, Any]]) -> list[RuleDetail]:
        """
        批量更新规则明细

        Args:
            rule_key: 规则模板键
            updates: 更新数据列表，每个元素必须包含rule_id字段

        Returns:
            List[RuleDetail]: 更新后的规则明细对象列表
        """
        try:
            template = self._get_template_by_key(rule_key)
            field_metadata_list = template.get_field_metadata_list() if template else []

            updated_details = []
            errors = []

            for i, update_data in enumerate(updates):
                try:
                    rule_id = update_data.get("rule_id")
                    if not rule_id:
                        errors.append(f"第{i+1}条更新数据缺少rule_id")
                        continue

                    # 获取现有规则明细
                    rule_detail = self.get_rule_detail(rule_key, rule_id)
                    if not rule_detail:
                        errors.append(f"第{i+1}条数据对应的规则明细不存在: rule_id={rule_id}")
                        continue

                    # 数据标准化和验证
                    normalized_data = self.data_mapping_engine.normalize_field_names(update_data)
                    validation_result = self.data_mapping_engine.validate_data(normalized_data, rule_key)

                    if not validation_result["valid"]:
                        errors.append(f"第{i+1}条数据验证失败: {', '.join(validation_result['errors'])}")
                        continue

                    # 合并数据
                    rule_detail.merge_from_dict(normalized_data, field_metadata_list)

                    # 详细验证
                    detail_validation = rule_detail.validate_data(field_metadata_list)
                    if not detail_validation["valid"]:
                        errors.append(f"第{i+1}条数据详细验证失败: {', '.join(detail_validation['errors'])}")
                        continue

                    updated_details.append(rule_detail)

                except Exception as e:
                    errors.append(f"第{i+1}条数据更新失败: {str(e)}")

            # 检查是否有错误
            if errors:
                raise ServiceError(
                    f"批量更新失败: {'; '.join(errors)}",
                    error_code="BATCH_UPDATE_FAILED",
                    details={"rule_key": rule_key, "errors": errors, "failed_count": len(errors)},
                )

            # 保存更改
            self.session.flush()

            logger.info(f"批量更新规则明细成功: rule_key={rule_key}, 更新{len(updated_details)}条记录")
            return updated_details

        except Exception as e:
            logger.error(f"批量更新规则明细失败: rule_key={rule_key}, error={e}")
            raise

    def upsert_rule_detail(self, rule_key: str, data: dict[str, Any]) -> tuple[RuleDetail, str]:
        """
        Upsert规则明细 - 支持插入、更新、跳过三种操作

        Args:
            rule_key: 规则模板键
            data: 规则明细数据，必须包含rule_id字段

        Returns:
            Tuple[RuleDetail, str]: (规则明细对象, 操作类型: 'CREATED'|'UPDATED'|'SKIPPED')

        Raises:
            ServiceError: 当操作失败时
        """
        start_time = time.time()
        operation = "upsert_rule_detail"

        try:
            # 1. 验证输入参数
            if not data or not isinstance(data, dict):
                raise ServiceError("数据不能为空且必须是字典格式", error_code="INVALID_INPUT")

            rule_id = data.get("rule_id")
            if not rule_id:
                raise ServiceError("数据中缺少必需的rule_id字段", error_code="MISSING_RULE_ID")

            # 2. 验证模板并获取元数据
            template, field_metadata_list = self._prepare_template_metadata(rule_key)

            # 3. 查找现有记录
            existing_detail = self.get_rule_detail(rule_key, rule_id)

            # 4. 验证和处理数据
            validated_data = self._process_and_validate_data(data, rule_key, field_metadata_list)

            if existing_detail is None:
                # 5a. 不存在记录，创建新记录
                rule_detail = self._create_rule_detail_object(validated_data)
                self._save_rule_detail(rule_detail)

                logger.info(f"Upsert创建新记录: rule_key={rule_key}, rule_id={rule_id}")
                return rule_detail, "CREATED"

            else:
                # 5b. 记录存在，检查是否需要更新
                changes = self._detect_data_changes(existing_detail, validated_data, field_metadata_list)

                if not changes:
                    # 数据完全一致，跳过更新
                    logger.info(f"Upsert跳过更新（数据一致）: rule_key={rule_key}, rule_id={rule_id}")
                    return existing_detail, "SKIPPED"

                else:
                    # 数据有变化，执行更新
                    existing_detail.merge_from_dict(validated_data, field_metadata_list)

                    # 详细验证
                    detail_validation = existing_detail.validate_data(field_metadata_list)
                    if not detail_validation["valid"]:
                        raise ServiceError(
                            f"更新数据验证失败: {', '.join(detail_validation['errors'])}",
                            error_code="UPDATE_VALIDATION_FAILED",
                            details={"rule_key": rule_key, "rule_id": rule_id, "errors": detail_validation["errors"]},
                        )

                    self.session.flush()

                    logger.info(
                        f"Upsert更新记录: rule_key={rule_key}, rule_id={rule_id}, 变更字段: {list(changes.keys())}"
                    )
                    return existing_detail, "UPDATED"

        except ServiceError:
            raise
        except Exception as e:
            raise self._handle_service_error(operation, e, rule_key, rule_id) from None

    def batch_upsert_rule_details(self, rule_key: str, data_list: list[dict[str, Any]]) -> dict[str, Any]:
        """
        批量Upsert规则明细

        Args:
            rule_key: 规则模板键
            data_list: 规则明细数据列表，每个元素必须包含rule_id字段

        Returns:
            Dict[str, Any]: 操作结果统计
            {
                "total_count": int,      # 总处理数量
                "created_count": int,    # 新建数量
                "updated_count": int,    # 更新数量
                "skipped_count": int,    # 跳过数量
                "failed_count": int,     # 失败数量
                "errors": List[str],     # 错误信息列表
                "created_details": List[RuleDetail],  # 新建的记录
                "updated_details": List[RuleDetail],  # 更新的记录
                "skipped_details": List[RuleDetail]   # 跳过的记录
            }

        Raises:
            ServiceError: 当批量操作失败时
        """
        start_time = time.time()
        operation = "batch_upsert_rule_details"

        try:
            # 1. 验证输入参数
            if not data_list or not isinstance(data_list, list):
                raise ServiceError("数据列表不能为空且必须是列表格式", error_code="INVALID_INPUT")

            # 2. 验证模板并获取元数据
            template, field_metadata_list = self._prepare_template_metadata(rule_key)

            # 3. 初始化统计信息
            result = {
                "total_count": len(data_list),
                "created_count": 0,
                "updated_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "errors": [],
                "created_details": [],
                "updated_details": [],
                "skipped_details": [],
            }

            # 4. 逐条处理数据
            for i, data in enumerate(data_list):
                try:
                    rule_detail, operation_type = self.upsert_rule_detail(rule_key, data)

                    # 统计操作结果
                    if operation_type == "CREATED":
                        result["created_count"] += 1
                        result["created_details"].append(rule_detail)
                    elif operation_type == "UPDATED":
                        result["updated_count"] += 1
                        result["updated_details"].append(rule_detail)
                    elif operation_type == "SKIPPED":
                        result["skipped_count"] += 1
                        result["skipped_details"].append(rule_detail)

                except Exception as e:
                    result["failed_count"] += 1
                    error_msg = f"第{i+1}条数据处理失败: {str(e)}"
                    result["errors"].append(error_msg)
                    logger.warning(error_msg)

            # 5. 记录操作结果
            processing_time = time.time() - start_time
            logger.info(
                f"批量Upsert完成: rule_key={rule_key}, "
                f"总数={result['total_count']}, "
                f"新建={result['created_count']}, "
                f"更新={result['updated_count']}, "
                f"跳过={result['skipped_count']}, "
                f"失败={result['failed_count']}, "
                f"耗时={processing_time:.3f}s"
            )

            return result

        except ServiceError:
            raise
        except Exception as e:
            raise self._handle_service_error(operation, e, rule_key) from None

    def batch_delete_rule_details(self, rule_key: str, rule_ids: list[str]) -> int:
        """
        批量删除规则明细

        Args:
            rule_key: 规则模板键
            rule_ids: 规则ID列表

        Returns:
            int: 实际删除的记录数
        """
        try:
            deleted_count = (
                self.session.query(RuleDetail)
                .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.rule_id.in_(rule_ids)))
                .delete(synchronize_session=False)
            )

            self.session.flush()

            logger.info(f"批量删除规则明细成功: rule_key={rule_key}, 删除{deleted_count}条记录")
            return deleted_count

        except Exception as e:
            logger.error(f"批量删除规则明细失败: rule_key={rule_key}, error={e}")
            raise

    def get_rule_detail_statistics(self, rule_key: str) -> dict[str, Any]:
        """
        获取规则明细统计信息

        Args:
            rule_key: 规则模板键

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 基础统计
            total_count = self.session.query(func.count(RuleDetail.id)).filter(RuleDetail.rule_key == rule_key).scalar()

            # 按状态统计
            status_stats = (
                self.session.query(RuleDetail.status, func.count(RuleDetail.id))
                .filter(RuleDetail.rule_key == rule_key)
                .group_by(RuleDetail.status)
                .all()
            )

            status_counts = {status.value: count for status, count in status_stats}

            # 按错误级别统计
            level_stats = (
                self.session.query(RuleDetail.level1, func.count(RuleDetail.id))
                .filter(RuleDetail.rule_key == rule_key)
                .group_by(RuleDetail.level1)
                .all()
            )

            level_counts = {level: count for level, count in level_stats}

            statistics = {
                "rule_key": rule_key,
                "total_count": total_count,
                "status_distribution": status_counts,
                "level1_distribution": level_counts,
                "last_updated": None,
            }

            # 获取最后更新时间
            last_updated = (
                self.session.query(func.max(RuleDetail.updated_at)).filter(RuleDetail.rule_key == rule_key).scalar()
            )

            if last_updated:
                # 确保时间对象有isoformat方法，防止测试中的类型错误
                if hasattr(last_updated, "isoformat"):
                    statistics["last_updated"] = last_updated.isoformat()
                else:
                    # 处理测试环境或其他情况下的非datetime对象
                    statistics["last_updated"] = str(last_updated) if last_updated else None

            logger.debug(f"获取规则明细统计信息: rule_key={rule_key}, total={total_count}")
            return statistics

        except Exception as e:
            logger.error(f"获取规则明细统计信息失败: rule_key={rule_key}, error={e}")
            raise

    def _get_template_by_key(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板"""
        return (
            self.session.query(RuleTemplate)
            .options(selectinload(RuleTemplate.field_metadata))
            .filter(RuleTemplate.rule_key == rule_key)
            .first()
        )

    @lru_cache(maxsize=128)
    def _get_template_by_key_cached(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板（带LRU缓存）"""
        return self._get_template_by_key(rule_key)

    @lru_cache(maxsize=256)
    def _get_field_metadata_cached(self, rule_key: str) -> list[RuleFieldMetadata]:
        """获取字段元数据（带LRU缓存）"""
        template = self._get_template_by_key_cached(rule_key)
        if template:
            return template.get_field_metadata_list()
        else:
            return []

    def _validate_rule_data(
        self, data: dict[str, Any], rule_key: str, field_metadata_list: list[RuleFieldMetadata]
    ) -> ValidationResult:
        """验证规则数据"""
        try:
            # 1. 数据标准化
            normalized_data = self.data_mapping_engine.normalize_field_names(data)

            # 2. 基础验证
            validation_result = self.data_mapping_engine.validate_data(normalized_data, rule_key)

            if not validation_result["valid"]:
                return ValidationResult(
                    valid=False, errors=validation_result["errors"], warnings=validation_result.get("warnings", [])
                )

            # 3. 元数据验证
            errors = []
            warnings = []

            for metadata in field_metadata_list:
                field_name = metadata.field_name
                field_value = normalized_data.get(field_name)

                # 验证必填字段
                if metadata.is_required and (field_value is None or field_value == ""):
                    chinese_name = self.field_mapping_manager.get_chinese_name(field_name)
                    errors.append(f"必填字段 '{chinese_name}' 不能为空")
                    continue

                # 验证字段值
                if field_value is not None:
                    field_validation = metadata.validate_field_value(field_value)
                    if not field_validation["valid"]:
                        errors.extend(field_validation["errors"])
                    if field_validation.get("warnings"):
                        warnings.extend(field_validation["warnings"])

            return ValidationResult(valid=len(errors) == 0, errors=errors, warnings=warnings, data=normalized_data)

        except Exception as e:
            return ValidationResult(valid=False, errors=[f"数据验证过程中发生错误: {str(e)}"])

    def clear_cache(self):
        """清空缓存"""
        # 清空LRU缓存
        self._get_template_by_key_cached.cache_clear()
        self._get_field_metadata_cached.cache_clear()

        # 清空旧的字典缓存（向后兼容）
        self._template_cache.clear()
        self._metadata_cache.clear()

        logger.info("服务缓存已清空")

    # ==================== 私有辅助方法 ====================

    def _validate_create_inputs(self, rule_key: str, data: dict[str, Any]) -> None:
        """验证创建规则明细的输入参数"""
        if not rule_key or not isinstance(rule_key, str):
            raise ServiceError(
                "规则模板键不能为空且必须是字符串类型",
                error_code="INVALID_RULE_KEY",
                details={"rule_key": rule_key, "type": type(rule_key).__name__},
            )

        if not isinstance(data, dict):
            raise ServiceError(
                "规则明细数据必须是字典类型", error_code="INVALID_DATA_TYPE", details={"data_type": type(data).__name__}
            )

        if not data:
            raise ServiceError("规则明细数据不能为空", error_code="EMPTY_DATA", details={"rule_key": rule_key})

    def _prepare_template_metadata(self, rule_key: str) -> tuple[RuleTemplate, list[RuleFieldMetadata]]:
        """准备模板和元数据"""
        template = self._get_template_by_key_cached(rule_key)
        if not template:
            raise ServiceError(
                f"规则模板 '{rule_key}' 不存在", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
            )

        field_metadata_list = self._get_field_metadata_cached(rule_key)
        return template, field_metadata_list

    def _process_and_validate_data(
        self, data: dict[str, Any], rule_key: str, field_metadata_list: list[RuleFieldMetadata]
    ) -> dict[str, Any]:
        """处理和验证数据"""
        validation_result = self._validate_rule_data(data, rule_key, field_metadata_list)
        if not validation_result.valid:
            raise ServiceError(
                f"数据验证失败: {', '.join(validation_result.errors)}",
                error_code="VALIDATION_FAILED",
                details={"rule_key": rule_key, "errors": validation_result.errors},
            )
        return validation_result.data

    def _create_rule_detail_object(self, validated_data: dict[str, Any]) -> RuleDetail:
        """创建规则明细对象"""
        # 分离固定字段和扩展字段
        fixed_fields, extended_fields = self.data_mapping_engine.separate_fields(validated_data)

        # 创建规则明细对象
        rule_detail = RuleDetail(**fixed_fields)
        if extended_fields:
            rule_detail.update_extended_fields(extended_fields)

        return rule_detail

    def _save_rule_detail(self, rule_detail: RuleDetail) -> None:
        """保存规则明细到数据库"""
        self.session.add(rule_detail)
        self.session.flush()  # 获取ID但不提交事务

    def _detect_data_changes(
        self, existing_detail: RuleDetail, new_data: dict[str, Any], field_metadata_list: list[RuleFieldMetadata]
    ) -> dict[str, tuple[Any, Any]]:
        """
        检测数据变化

        Args:
            existing_detail: 现有的规则明细记录
            new_data: 新的数据
            field_metadata_list: 字段元数据列表

        Returns:
            Dict[str, Tuple[Any, Any]]: 变化的字段映射 {字段名: (旧值, 新值)}
        """
        changes = {}

        # 分离固定字段和扩展字段
        fixed_fields, extended_fields = self.data_mapping_engine.separate_fields(new_data)

        # 1. 检查固定字段的变化
        for field_name, new_value in fixed_fields.items():
            if hasattr(existing_detail, field_name):
                old_value = getattr(existing_detail, field_name)

                # 特殊处理数组字段
                if self.data_mapping_engine._is_array_field(field_name):
                    # 数组字段比较（转换为列表后比较）
                    old_list = self._convert_to_list(old_value)
                    new_list = self._convert_to_list(new_value)

                    if old_list != new_list:
                        changes[field_name] = (old_list, new_list)

                # 特殊处理字符串字段（去除空白字符后比较）
                elif isinstance(old_value, str) and isinstance(new_value, str):
                    old_stripped = old_value.strip() if old_value else ""
                    new_stripped = new_value.strip() if new_value else ""

                    if old_stripped != new_stripped:
                        changes[field_name] = (old_value, new_value)

                # 处理None值的比较
                elif old_value != new_value:
                    # 将空字符串和None视为相等
                    if (old_value is None or old_value == "") and (new_value is None or new_value == ""):
                        continue
                    changes[field_name] = (old_value, new_value)

        # 2. 检查扩展字段的变化
        if extended_fields:
            import json

            # 获取现有的扩展字段
            existing_extended = {}
            if existing_detail.extended_fields:
                try:
                    existing_extended = json.loads(existing_detail.extended_fields)
                except (json.JSONDecodeError, TypeError):
                    existing_extended = {}

            # 比较扩展字段
            for field_name, new_value in extended_fields.items():
                old_value = existing_extended.get(field_name)

                if old_value != new_value:
                    # 将空字符串和None视为相等
                    if (old_value is None or old_value == "") and (new_value is None or new_value == ""):
                        continue
                    changes[f"extended_fields.{field_name}"] = (old_value, new_value)

        return changes

    def _convert_to_list(self, value: Any) -> list:
        """
        将值转换为列表格式，用于数组字段比较

        Args:
            value: 待转换的值

        Returns:
            List: 转换后的列表
        """
        if value is None:
            return []

        if isinstance(value, list):
            return value

        if isinstance(value, str):
            # 处理逗号分隔的字符串
            if not value.strip():
                return []
            return [item.strip() for item in value.split(",") if item.strip()]

        # 其他类型转换为单元素列表
        return [value]

    def _log_create_success(self, operation: str, rule_key: str, rule_detail: RuleDetail, start_time: float) -> None:
        """记录创建成功日志"""
        duration = time.time() - start_time
        self._log_operation(operation, rule_key, rule_detail.rule_id, duration, success=True)
        logger.info(f"成功创建规则明细: rule_key={rule_key}, rule_id={rule_detail.rule_id}")
