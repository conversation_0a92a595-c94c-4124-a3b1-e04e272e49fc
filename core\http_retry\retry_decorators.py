"""
HTTP重试装饰器
提供同步和异步版本的重试装饰器，简化重试逻辑的使用
"""

import asyncio
import functools
import logging
import time
from collections.abc import Callable
from typing import Any

from config.retry_config import get_retry_config_manager

from .backoff import BackoffStrategy, calculate_backoff_delay
from .circuit_breaker import get_circuit_breaker_manager
from .retry_client import CircuitBreakerOpenError, RetryExhaustedError

logger = logging.getLogger(__name__)


def retry_on_exception(
    max_attempts: int | None = None,
    base_delay: float | None = None,
    max_delay: float | None = None,
    backoff_factor: float | None = None,
    jitter: bool | None = None,
    retryable_exceptions: tuple[type[Exception], ...] | None = None,
    circuit_breaker_name: str | None = None,
    enable_circuit_breaker: bool = True
):
    """
    异步函数重试装饰器

    Args:
        max_attempts: 最大重试次数
        base_delay: 基础延迟时间
        max_delay: 最大延迟时间
        backoff_factor: 退避因子
        jitter: 是否启用抖动
        retryable_exceptions: 可重试的异常类型
        circuit_breaker_name: 断路器名称
        enable_circuit_breaker: 是否启用断路器
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 获取配置
            config_manager = get_retry_config_manager()
            retry_config = _get_retry_config(config_manager, {
                "max_attempts": max_attempts,
                "base_delay": base_delay,
                "max_delay": max_delay,
                "backoff_factor": backoff_factor,
                "jitter": jitter
            })

            # 初始化断路器
            circuit_breaker = None
            if enable_circuit_breaker:
                try:
                    circuit_manager = get_circuit_breaker_manager()
                    breaker_name = circuit_breaker_name or f"{func.__module__}.{func.__name__}"
                    circuit_breaker = circuit_manager.get_circuit_breaker(breaker_name)
                except Exception as e:
                    logger.warning(f"断路器初始化失败: {e}")

            # 检查断路器状态
            if circuit_breaker and not circuit_breaker.can_execute():
                raise CircuitBreakerOpenError(
                    f"断路器 '{circuit_breaker.name}' 已打开",
                    circuit_breaker.name
                )

            last_exception = None

            for attempt in range(retry_config["max_attempts"]):
                try:
                    start_time = time.time()

                    # 执行函数
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)

                    # 记录成功
                    if circuit_breaker:
                        response_time = time.time() - start_time
                        circuit_breaker.record_success(response_time)

                    # 记录重试成功日志
                    if attempt > 0:
                        logger.info(
                            f"函数重试成功: {func.__name__} (第 {attempt + 1} 次尝试)",
                            extra={
                                "function": func.__name__,
                                "total_attempts": attempt + 1,
                                "response_time": time.time() - start_time
                            }
                        )

                    return result

                except Exception as e:
                    last_exception = e

                    # 检查是否可重试
                    if not _is_retryable_exception(e, retryable_exceptions):
                        if circuit_breaker:
                            circuit_breaker.record_failure(type(e).__name__)
                        raise e

                    # 记录失败
                    if circuit_breaker:
                        circuit_breaker.record_failure(type(e).__name__)

                    # 如果不是最后一次尝试，进行重试
                    if attempt < retry_config["max_attempts"] - 1:
                        delay = calculate_backoff_delay(
                            attempt=attempt,
                            base_delay=retry_config["base_delay"],
                            max_delay=retry_config["max_delay"],
                            backoff_factor=retry_config["backoff_factor"],
                            jitter=retry_config["jitter"],
                            strategy=BackoffStrategy.EXPONENTIAL
                        )

                        logger.info(
                            f"函数重试: {func.__name__} (第 {attempt + 1}/{retry_config['max_attempts']} 次)",
                            extra={
                                "function": func.__name__,
                                "attempt": attempt + 1,
                                "max_attempts": retry_config["max_attempts"],
                                "retry_delay": delay,
                                "error_type": type(e).__name__
                            }
                        )

                        await asyncio.sleep(delay)
                        continue

            # 所有重试都失败了
            logger.warning(
                f"函数重试耗尽: {func.__name__} ({retry_config['max_attempts']} 次)",
                extra={
                    "function": func.__name__,
                    "max_attempts": retry_config["max_attempts"],
                    "last_error": str(last_exception),
                    "error_type": type(last_exception).__name__
                }
            )

            raise RetryExhaustedError(
                f"函数 {func.__name__} 重试 {retry_config['max_attempts']} 次后仍然失败: {str(last_exception)}",
                last_exception,
                retry_config["max_attempts"]
            )

        return async_wrapper

    return decorator


def sync_retry_on_exception(
    max_attempts: int | None = None,
    base_delay: float | None = None,
    max_delay: float | None = None,
    backoff_factor: float | None = None,
    jitter: bool | None = None,
    retryable_exceptions: tuple[type[Exception], ...] | None = None,
    circuit_breaker_name: str | None = None,
    enable_circuit_breaker: bool = True
):
    """
    同步函数重试装饰器

    Args:
        max_attempts: 最大重试次数
        base_delay: 基础延迟时间
        max_delay: 最大延迟时间
        backoff_factor: 退避因子
        jitter: 是否启用抖动
        retryable_exceptions: 可重试的异常类型
        circuit_breaker_name: 断路器名称
        enable_circuit_breaker: 是否启用断路器
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 获取配置
            config_manager = get_retry_config_manager()
            retry_config = _get_retry_config(config_manager, {
                "max_attempts": max_attempts,
                "base_delay": base_delay,
                "max_delay": max_delay,
                "backoff_factor": backoff_factor,
                "jitter": jitter
            })

            # 初始化断路器
            circuit_breaker = None
            if enable_circuit_breaker:
                try:
                    circuit_manager = get_circuit_breaker_manager()
                    breaker_name = circuit_breaker_name or f"{func.__module__}.{func.__name__}"
                    circuit_breaker = circuit_manager.get_circuit_breaker(breaker_name)
                except Exception as e:
                    logger.warning(f"断路器初始化失败: {e}")

            # 检查断路器状态
            if circuit_breaker and not circuit_breaker.can_execute():
                raise CircuitBreakerOpenError(
                    f"断路器 '{circuit_breaker.name}' 已打开",
                    circuit_breaker.name
                )

            last_exception = None

            for attempt in range(retry_config["max_attempts"]):
                try:
                    start_time = time.time()

                    # 执行函数
                    result = func(*args, **kwargs)

                    # 记录成功
                    if circuit_breaker:
                        response_time = time.time() - start_time
                        circuit_breaker.record_success(response_time)

                    # 记录重试成功日志
                    if attempt > 0:
                        logger.info(
                            f"函数重试成功: {func.__name__} (第 {attempt + 1} 次尝试)",
                            extra={
                                "function": func.__name__,
                                "total_attempts": attempt + 1,
                                "response_time": time.time() - start_time
                            }
                        )

                    return result

                except Exception as e:
                    last_exception = e

                    # 检查是否可重试
                    if not _is_retryable_exception(e, retryable_exceptions):
                        if circuit_breaker:
                            circuit_breaker.record_failure(type(e).__name__)
                        raise e

                    # 记录失败
                    if circuit_breaker:
                        circuit_breaker.record_failure(type(e).__name__)

                    # 如果不是最后一次尝试，进行重试
                    if attempt < retry_config["max_attempts"] - 1:
                        delay = calculate_backoff_delay(
                            attempt=attempt,
                            base_delay=retry_config["base_delay"],
                            max_delay=retry_config["max_delay"],
                            backoff_factor=retry_config["backoff_factor"],
                            jitter=retry_config["jitter"],
                            strategy=BackoffStrategy.EXPONENTIAL
                        )

                        logger.info(
                            f"函数重试: {func.__name__} (第 {attempt + 1}/{retry_config['max_attempts']} 次)",
                            extra={
                                "function": func.__name__,
                                "attempt": attempt + 1,
                                "max_attempts": retry_config["max_attempts"],
                                "retry_delay": delay,
                                "error_type": type(e).__name__
                            }
                        )

                        time.sleep(delay)
                        continue

            # 所有重试都失败了
            logger.warning(
                f"函数重试耗尽: {func.__name__} ({retry_config['max_attempts']} 次)",
                extra={
                    "function": func.__name__,
                    "max_attempts": retry_config["max_attempts"],
                    "last_error": str(last_exception),
                    "error_type": type(last_exception).__name__
                }
            )

            raise RetryExhaustedError(
                f"函数 {func.__name__} 重试 {retry_config['max_attempts']} 次后仍然失败: {str(last_exception)}",
                last_exception,
                retry_config["max_attempts"]
            )

        return sync_wrapper

    return decorator


def _get_retry_config(config_manager, custom_config: dict[str, Any]) -> dict[str, Any]:
    """获取重试配置"""
    # 默认配置
    default_config = {
        "max_attempts": 3,
        "base_delay": 1.0,
        "max_delay": 60.0,
        "backoff_factor": 2.0,
        "jitter": True
    }

    # 从配置管理器获取配置
    if config_manager:
        try:
            config = config_manager.get_retry_config()
            default_config.update({
                "max_attempts": config.max_attempts,
                "base_delay": config.base_delay,
                "max_delay": config.max_delay,
                "backoff_factor": config.backoff_factor,
                "jitter": config.jitter
            })
        except Exception as e:
            logger.warning(f"获取重试配置失败，使用默认配置: {e}")

    # 应用自定义配置
    for key, value in custom_config.items():
        if value is not None:
            default_config[key] = value

    return default_config


def _is_retryable_exception(exception: Exception, retryable_exceptions: tuple[type[Exception], ...] | None) -> bool:
    """判断异常是否可重试"""
    if retryable_exceptions:
        return isinstance(exception, retryable_exceptions)

    # 默认可重试的异常类型
    default_retryable = (
        ConnectionError,
        TimeoutError,
        OSError,  # 包含网络相关错误
    )

    # 检查httpx异常（如果可用）
    try:
        import httpx
        default_retryable += (httpx.ConnectError, httpx.TimeoutException)
    except ImportError:
        pass

    return isinstance(exception, default_retryable)
