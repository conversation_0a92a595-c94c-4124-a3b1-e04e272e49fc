"""
性能优化组件降级机制核心架构
定义降级策略、状态管理、事件驱动机制等核心组件
"""

import threading
import time
from abc import ABC, abstractmethod
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any

from core.logging.logging_system import log as logger


class DegradationLevel(Enum):
    """降级级别枚举"""

    L0_NORMAL = "normal"  # L0: 正常状态，所有功能启用
    L1_LIGHT = "light_degradation"  # L1: 轻度降级，减少部分资源使用
    L2_MODERATE = "moderate_degradation"  # L2: 中度降级，禁用非核心功能
    L3_SEVERE = "severe_degradation"  # L3: 重度降级，仅保留核心功能


class DegradationTriggerType(Enum):
    """降级触发类型枚举"""

    CPU_USAGE = "cpu_usage"  # CPU使用率触发
    MEMORY_USAGE = "memory_usage"  # 内存使用率触发
    ERROR_RATE = "error_rate"  # 错误率触发
    QUEUE_LENGTH = "queue_length"  # 队列长度触发
    RESPONSE_TIME = "response_time"  # 响应时间触发
    MANUAL = "manual"  # 手动触发
    COMPOSITE = "composite"  # 复合条件触发


class DegradationActionType(Enum):
    """降级动作类型枚举"""

    REDUCE_PROCESS_POOL = "reduce_process_pool"  # 减少进程池大小
    DISABLE_CACHE = "disable_cache"  # 禁用缓存
    REDUCE_CACHE_SIZE = "reduce_cache_size"  # 减少缓存大小
    DISABLE_OBJECT_POOL = "disable_object_pool"  # 禁用对象池
    REDUCE_OBJECT_POOL = "reduce_object_pool"  # 减少对象池大小
    DISABLE_OPTIMIZATION = "disable_optimization"  # 禁用性能优化
    REDUCE_CONCURRENCY = "reduce_concurrency"  # 减少并发度
    ENABLE_CIRCUIT_BREAKER = "enable_circuit_breaker"  # 启用断路器


class DegradationEventType(Enum):
    """降级事件类型枚举"""

    DEGRADATION_TRIGGERED = "degradation_triggered"  # 降级触发
    DEGRADATION_RECOVERED = "degradation_recovered"  # 降级恢复
    LEVEL_CHANGED = "level_changed"  # 级别变更
    ACTION_EXECUTED = "action_executed"  # 动作执行
    ACTION_FAILED = "action_failed"  # 动作失败
    MANUAL_OVERRIDE = "manual_override"  # 手动干预


@dataclass
class DegradationTrigger:
    """降级触发条件"""

    trigger_type: DegradationTriggerType
    threshold: float  # 触发阈值
    recovery_threshold: float  # 恢复阈值（支持滞后恢复）
    window_size: int = 10  # 滑动窗口大小
    min_samples: int = 3  # 最小样本数
    enabled: bool = True  # 是否启用

    def __post_init__(self):
        """验证配置"""
        if self.threshold <= 0:
            raise ValueError("threshold必须大于0")
        if self.recovery_threshold <= 0:
            raise ValueError("recovery_threshold必须大于0")
        if self.window_size <= 0:
            raise ValueError("window_size必须大于0")
        if self.min_samples <= 0:
            raise ValueError("min_samples必须大于0")


@dataclass
class DegradationAction:
    """降级动作定义"""

    action_type: DegradationActionType
    target_component: str  # 目标组件名称
    parameters: dict[str, Any] = field(default_factory=dict)  # 动作参数
    priority: int = 0  # 执行优先级（数字越大优先级越高）
    rollback_enabled: bool = True  # 是否支持回滚
    timeout: float = 30.0  # 执行超时时间

    def __post_init__(self):
        """验证配置"""
        if not self.target_component:
            raise ValueError("target_component不能为空")
        if self.timeout <= 0:
            raise ValueError("timeout必须大于0")


@dataclass
class DegradationEvent:
    """降级事件"""

    event_type: DegradationEventType
    timestamp: float
    level: DegradationLevel
    trigger_type: DegradationTriggerType | None = None
    trigger_value: float | None = None
    actions: list[DegradationAction] = field(default_factory=list)
    metadata: dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """设置默认时间戳"""
        if self.timestamp <= 0:
            self.timestamp = time.time()


@dataclass
class DegradationState:
    """降级状态"""

    current_level: DegradationLevel = DegradationLevel.L0_NORMAL
    previous_level: DegradationLevel = DegradationLevel.L0_NORMAL
    last_change_time: float = field(default_factory=time.time)
    active_triggers: set[DegradationTriggerType] = field(default_factory=set)
    executed_actions: list[DegradationAction] = field(default_factory=list)
    is_manual_override: bool = False
    override_reason: str | None = None

    def is_degraded(self) -> bool:
        """检查是否处于降级状态"""
        return self.current_level != DegradationLevel.L0_NORMAL

    def get_degradation_duration(self) -> float:
        """获取降级持续时间（秒）"""
        if not self.is_degraded():
            return 0.0
        return time.time() - self.last_change_time


class DegradationAware(ABC):
    """降级感知接口

    所有支持降级的组件都应该实现此接口
    """

    @abstractmethod
    def on_degradation_event(self, event: DegradationEvent) -> bool:
        """
        处理降级事件

        Args:
            event: 降级事件

        Returns:
            bool: 是否成功处理事件
        """
        pass

    @abstractmethod
    def get_component_name(self) -> str:
        """
        获取组件名称

        Returns:
            str: 组件名称
        """
        pass

    @abstractmethod
    def get_current_status(self) -> dict[str, Any]:
        """
        获取组件当前状态

        Returns:
            Dict[str, Any]: 组件状态信息
        """
        pass


class DegradationEventListener(ABC):
    """降级事件监听器接口"""

    @abstractmethod
    def on_event(self, event: DegradationEvent) -> None:
        """
        处理降级事件

        Args:
            event: 降级事件
        """
        pass


@dataclass
class DegradationMetrics:
    """降级指标统计"""

    total_degradations: int = 0  # 总降级次数
    degradation_by_level: dict[DegradationLevel, int] = field(default_factory=dict)
    degradation_by_trigger: dict[DegradationTriggerType, int] = field(default_factory=dict)
    total_recovery_time: float = 0.0  # 总恢复时间
    average_recovery_time: float = 0.0  # 平均恢复时间
    last_degradation_time: float | None = None
    successful_actions: int = 0  # 成功执行的动作数
    failed_actions: int = 0  # 失败的动作数

    def record_degradation(self, level: DegradationLevel, trigger: DegradationTriggerType):
        """记录降级事件"""
        self.total_degradations += 1
        self.degradation_by_level[level] = self.degradation_by_level.get(level, 0) + 1
        self.degradation_by_trigger[trigger] = self.degradation_by_trigger.get(trigger, 0) + 1
        self.last_degradation_time = time.time()

    def record_recovery(self, recovery_time: float):
        """记录恢复事件"""
        self.total_recovery_time += recovery_time
        if self.total_degradations > 0:
            self.average_recovery_time = self.total_recovery_time / self.total_degradations

    def record_action_result(self, success: bool):
        """记录动作执行结果"""
        if success:
            self.successful_actions += 1
        else:
            self.failed_actions += 1


class DegradationStateMachine:
    """降级状态机

    管理降级状态转换，确保状态转换的原子性和一致性
    """

    def __init__(self):
        self._state = DegradationState()
        self._lock = threading.RLock()
        self._transition_history: deque = deque(maxlen=100)

        # 定义允许的状态转换
        self._allowed_transitions = {
            DegradationLevel.L0_NORMAL: {DegradationLevel.L1_LIGHT},
            DegradationLevel.L1_LIGHT: {DegradationLevel.L0_NORMAL, DegradationLevel.L2_MODERATE},
            DegradationLevel.L2_MODERATE: {DegradationLevel.L1_LIGHT, DegradationLevel.L3_SEVERE},
            DegradationLevel.L3_SEVERE: {DegradationLevel.L2_MODERATE},
        }

    def get_current_state(self) -> DegradationState:
        """获取当前状态（只读副本）"""
        with self._lock:
            # 返回状态的副本，避免外部修改
            return DegradationState(
                current_level=self._state.current_level,
                previous_level=self._state.previous_level,
                last_change_time=self._state.last_change_time,
                active_triggers=self._state.active_triggers.copy(),
                executed_actions=self._state.executed_actions.copy(),
                is_manual_override=self._state.is_manual_override,
                override_reason=self._state.override_reason,
            )

    def can_transition_to(self, target_level: DegradationLevel) -> bool:
        """检查是否可以转换到目标级别"""
        with self._lock:
            current_level = self._state.current_level

            # 手动干预可以转换到任何级别
            if self._state.is_manual_override:
                return True

            # 检查是否在允许的转换列表中
            allowed = self._allowed_transitions.get(current_level, set())
            return target_level in allowed or target_level == current_level

    def transition_to(
        self,
        target_level: DegradationLevel,
        trigger_type: DegradationTriggerType | None = None,
        is_manual: bool = False,
        reason: str | None = None,
    ) -> bool:
        """
        转换到目标降级级别

        Args:
            target_level: 目标降级级别
            trigger_type: 触发类型
            is_manual: 是否手动触发
            reason: 转换原因

        Returns:
            bool: 是否成功转换
        """
        with self._lock:
            current_level = self._state.current_level

            # 检查是否可以转换
            if not is_manual and not self.can_transition_to(target_level):
                logger.warning(
                    f"不允许从 {current_level.value} 转换到 {target_level.value}",
                    extra={"current_level": current_level.value, "target_level": target_level.value},
                )
                return False

            # 如果目标级别与当前级别相同，不需要转换
            if target_level == current_level:
                return True

            # 执行状态转换
            self._state.previous_level = current_level
            self._state.current_level = target_level
            self._state.last_change_time = time.time()
            self._state.is_manual_override = is_manual
            self._state.override_reason = reason

            # 更新活跃触发器
            if trigger_type:
                if target_level == DegradationLevel.L0_NORMAL:
                    # 恢复正常时清除触发器
                    self._state.active_triggers.discard(trigger_type)
                else:
                    # 降级时添加触发器
                    self._state.active_triggers.add(trigger_type)

            # 记录转换历史
            transition_record = {
                "timestamp": self._state.last_change_time,
                "from": current_level.value,
                "to": target_level.value,
                "trigger_type": trigger_type.value if trigger_type else None,
                "is_manual": is_manual,
                "reason": reason,
            }
            self._transition_history.append(transition_record)

            logger.info(
                f"降级状态转换: {current_level.value} -> {target_level.value}",
                extra={
                    "from_level": current_level.value,
                    "to_level": target_level.value,
                    "trigger_type": trigger_type.value if trigger_type else None,
                    "is_manual": is_manual,
                    "reason": reason,
                },
            )

            return True

    def add_executed_action(self, action: DegradationAction):
        """添加已执行的动作"""
        with self._lock:
            self._state.executed_actions.append(action)

    def clear_executed_actions(self):
        """清除已执行的动作列表"""
        with self._lock:
            self._state.executed_actions.clear()

    def get_transition_history(self, limit: int = 50) -> list[dict[str, Any]]:
        """获取状态转换历史"""
        with self._lock:
            return list(self._transition_history)[-limit:]


class DegradationEventManager:
    """降级事件管理器

    负责事件的发布、订阅和分发，支持异步事件处理
    """

    def __init__(self):
        self._listeners: dict[DegradationEventType, list[DegradationEventListener]] = {}
        self._component_listeners: dict[str, list[DegradationAware]] = {}
        self._lock = threading.RLock()
        self._event_history: deque = deque(maxlen=1000)
        self._metrics = DegradationMetrics()

    def subscribe(self, event_type: DegradationEventType, listener: DegradationEventListener):
        """
        订阅降级事件

        Args:
            event_type: 事件类型
            listener: 事件监听器
        """
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(listener)

            logger.debug(
                f"事件监听器已订阅: {event_type.value}",
                extra={"event_type": event_type.value, "listener": type(listener).__name__},
            )

    def unsubscribe(self, event_type: DegradationEventType, listener: DegradationEventListener):
        """
        取消订阅降级事件

        Args:
            event_type: 事件类型
            listener: 事件监听器
        """
        with self._lock:
            if event_type in self._listeners:
                try:
                    self._listeners[event_type].remove(listener)
                    logger.debug(
                        f"事件监听器已取消订阅: {event_type.value}",
                        extra={"event_type": event_type.value, "listener": type(listener).__name__},
                    )
                except ValueError:
                    logger.warning(
                        f"尝试取消订阅不存在的监听器: {event_type.value}",
                        extra={"event_type": event_type.value, "listener": type(listener).__name__},
                    )

    def register_component(self, component: DegradationAware):
        """
        注册降级感知组件

        Args:
            component: 降级感知组件
        """
        with self._lock:
            component_name = component.get_component_name()
            if component_name not in self._component_listeners:
                self._component_listeners[component_name] = []
            self._component_listeners[component_name].append(component)

            logger.info(
                f"降级感知组件已注册: {component_name}",
                extra={"component_name": component_name, "component_type": type(component).__name__},
            )

    def unregister_component(self, component: DegradationAware):
        """
        取消注册降级感知组件

        Args:
            component: 降级感知组件
        """
        with self._lock:
            component_name = component.get_component_name()
            if component_name in self._component_listeners:
                try:
                    self._component_listeners[component_name].remove(component)
                    logger.info(
                        f"降级感知组件已取消注册: {component_name}",
                        extra={"component_name": component_name, "component_type": type(component).__name__},
                    )
                except ValueError:
                    logger.warning(
                        f"尝试取消注册不存在的组件: {component_name}",
                        extra={"component_name": component_name, "component_type": type(component).__name__},
                    )

    def publish_event(self, event: DegradationEvent):
        """
        发布降级事件

        Args:
            event: 降级事件
        """
        with self._lock:
            # 记录事件历史
            self._event_history.append(event)

            # 更新指标
            self._update_metrics(event)

            logger.info(
                f"发布降级事件: {event.event_type.value}",
                extra={
                    "event_type": event.event_type.value,
                    "level": event.level.value,
                    "trigger_type": event.trigger_type.value if event.trigger_type else None,
                    "actions_count": len(event.actions),
                },
            )

        # 通知事件监听器（在锁外执行，避免死锁）
        self._notify_listeners(event)

        # 通知组件（在锁外执行，避免死锁）
        self._notify_components(event)

    def _notify_listeners(self, event: DegradationEvent):
        """通知事件监听器"""
        listeners = []
        with self._lock:
            listeners = self._listeners.get(event.event_type, []).copy()

        for listener in listeners:
            try:
                listener.on_event(event)
            except Exception as e:
                logger.error(
                    f"事件监听器处理失败: {type(listener).__name__}",
                    extra={"event_type": event.event_type.value, "listener": type(listener).__name__, "error": str(e)},
                    exc_info=True,
                )

    def _notify_components(self, event: DegradationEvent):
        """通知降级感知组件"""
        all_components = []
        with self._lock:
            for components in self._component_listeners.values():
                all_components.extend(components)

        for component in all_components:
            try:
                success = component.on_degradation_event(event)
                if not success:
                    logger.warning(
                        f"组件处理降级事件失败: {component.get_component_name()}",
                        extra={"component_name": component.get_component_name(), "event_type": event.event_type.value},
                    )
            except Exception as e:
                logger.error(
                    f"组件处理降级事件异常: {component.get_component_name()}",
                    extra={
                        "component_name": component.get_component_name(),
                        "event_type": event.event_type.value,
                        "error": str(e),
                    },
                    exc_info=True,
                )

    def _update_metrics(self, event: DegradationEvent):
        """更新事件指标"""
        if event.event_type == DegradationEventType.DEGRADATION_TRIGGERED:
            if event.trigger_type:
                self._metrics.record_degradation(event.level, event.trigger_type)
        elif event.event_type == DegradationEventType.ACTION_EXECUTED:
            self._metrics.record_action_result(True)
        elif event.event_type == DegradationEventType.ACTION_FAILED:
            self._metrics.record_action_result(False)

    def get_event_history(self, limit: int = 100) -> list[DegradationEvent]:
        """获取事件历史"""
        with self._lock:
            return list(self._event_history)[-limit:]

    def get_metrics(self) -> DegradationMetrics:
        """获取降级指标"""
        with self._lock:
            return self._metrics

    def clear_history(self):
        """清除事件历史"""
        with self._lock:
            self._event_history.clear()

    def get_registered_components(self) -> dict[str, int]:
        """获取已注册的组件信息"""
        with self._lock:
            return {name: len(components) for name, components in self._component_listeners.items()}


@dataclass
class DegradationStrategy:
    """降级策略配置

    定义特定降级级别下应该执行的动作和触发条件
    """

    level: DegradationLevel
    triggers: list[DegradationTrigger] = field(default_factory=list)
    actions: list[DegradationAction] = field(default_factory=list)
    description: str = ""
    enabled: bool = True

    def __post_init__(self):
        """验证策略配置"""
        if not self.triggers and self.level != DegradationLevel.L0_NORMAL:
            logger.warning(f"降级级别 {self.level.value} 没有配置触发条件")

        if not self.actions and self.level != DegradationLevel.L0_NORMAL:
            logger.warning(f"降级级别 {self.level.value} 没有配置降级动作")

    def add_trigger(self, trigger: DegradationTrigger):
        """添加触发条件"""
        self.triggers.append(trigger)

    def add_action(self, action: DegradationAction):
        """添加降级动作"""
        self.actions.append(action)
        # 按优先级排序
        self.actions.sort(key=lambda x: x.priority, reverse=True)

    def get_enabled_triggers(self) -> list[DegradationTrigger]:
        """获取启用的触发条件"""
        return [trigger for trigger in self.triggers if trigger.enabled]

    def get_actions_for_component(self, component_name: str) -> list[DegradationAction]:
        """获取特定组件的降级动作"""
        return [action for action in self.actions if action.target_component == component_name]


class DegradationStrategyManager:
    """降级策略管理器

    管理所有降级策略的配置和应用
    """

    def __init__(self):
        self._strategies: dict[DegradationLevel, DegradationStrategy] = {}
        self._lock = threading.RLock()
        self._initialize_default_strategies()

    def _initialize_default_strategies(self):
        """初始化默认降级策略"""
        # L0 正常状态 - 无需特殊配置
        self._strategies[DegradationLevel.L0_NORMAL] = DegradationStrategy(
            level=DegradationLevel.L0_NORMAL, description="正常状态，所有功能启用"
        )

        # L1 轻度降级策略
        l1_strategy = DegradationStrategy(level=DegradationLevel.L1_LIGHT, description="轻度降级，减少部分资源使用")

        # L1 触发条件
        l1_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.CPU_USAGE,
                threshold=75.0,
                recovery_threshold=65.0,
                window_size=10,
                min_samples=3,
            )
        )
        l1_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.MEMORY_USAGE,
                threshold=80.0,
                recovery_threshold=70.0,
                window_size=10,
                min_samples=3,
            )
        )

        # L1 降级动作
        l1_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_PROCESS_POOL,
                target_component="DynamicProcessPool",
                parameters={"reduction_factor": 0.75},
                priority=10,
            )
        )
        l1_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_CACHE_SIZE,
                target_component="IntelligentCache",
                parameters={"reduction_factor": 0.75},
                priority=8,
            )
        )

        self._strategies[DegradationLevel.L1_LIGHT] = l1_strategy

        # L2 中度降级策略
        l2_strategy = DegradationStrategy(level=DegradationLevel.L2_MODERATE, description="中度降级，禁用非核心功能")

        # L2 触发条件
        l2_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.CPU_USAGE,
                threshold=85.0,
                recovery_threshold=75.0,
                window_size=8,
                min_samples=3,
            )
        )
        l2_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.MEMORY_USAGE,
                threshold=90.0,
                recovery_threshold=80.0,
                window_size=8,
                min_samples=3,
            )
        )
        l2_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.ERROR_RATE,
                threshold=10.0,
                recovery_threshold=5.0,
                window_size=20,
                min_samples=5,
            )
        )

        # L2 降级动作
        l2_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_PROCESS_POOL,
                target_component="DynamicProcessPool",
                parameters={"reduction_factor": 0.5},
                priority=10,
            )
        )
        l2_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_CACHE_SIZE,
                target_component="IntelligentCache",
                parameters={"reduction_factor": 0.5},
                priority=9,
            )
        )
        l2_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_OBJECT_POOL,
                target_component="ObjectPool",
                parameters={"reduction_factor": 0.5},
                priority=8,
            )
        )

        self._strategies[DegradationLevel.L2_MODERATE] = l2_strategy

        # L3 重度降级策略
        l3_strategy = DegradationStrategy(level=DegradationLevel.L3_SEVERE, description="重度降级，仅保留核心功能")

        # L3 触发条件
        l3_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.CPU_USAGE,
                threshold=95.0,
                recovery_threshold=85.0,
                window_size=5,
                min_samples=3,
            )
        )
        l3_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.MEMORY_USAGE,
                threshold=95.0,
                recovery_threshold=90.0,
                window_size=5,
                min_samples=3,
            )
        )
        l3_strategy.add_trigger(
            DegradationTrigger(
                trigger_type=DegradationTriggerType.ERROR_RATE,
                threshold=20.0,
                recovery_threshold=10.0,
                window_size=15,
                min_samples=5,
            )
        )

        # L3 降级动作
        l3_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.REDUCE_PROCESS_POOL,
                target_component="DynamicProcessPool",
                parameters={"reduction_factor": 0.25},
                priority=10,
            )
        )
        l3_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.DISABLE_CACHE,
                target_component="IntelligentCache",
                parameters={},
                priority=9,
            )
        )
        l3_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.DISABLE_OBJECT_POOL,
                target_component="ObjectPool",
                parameters={},
                priority=8,
            )
        )
        l3_strategy.add_action(
            DegradationAction(
                action_type=DegradationActionType.ENABLE_CIRCUIT_BREAKER,
                target_component="CircuitBreaker",
                parameters={"aggressive_mode": True},
                priority=7,
            )
        )

        self._strategies[DegradationLevel.L3_SEVERE] = l3_strategy

        logger.info("默认降级策略已初始化")

    def get_strategy(self, level: DegradationLevel) -> DegradationStrategy | None:
        """获取指定级别的降级策略"""
        with self._lock:
            return self._strategies.get(level)

    def set_strategy(self, strategy: DegradationStrategy):
        """设置降级策略"""
        with self._lock:
            self._strategies[strategy.level] = strategy
            logger.info(f"降级策略已更新: {strategy.level.value}")

    def get_all_strategies(self) -> dict[DegradationLevel, DegradationStrategy]:
        """获取所有降级策略"""
        with self._lock:
            return self._strategies.copy()

    def enable_strategy(self, level: DegradationLevel):
        """启用指定级别的降级策略"""
        with self._lock:
            if level in self._strategies:
                self._strategies[level].enabled = True
                logger.info(f"降级策略已启用: {level.value}")

    def disable_strategy(self, level: DegradationLevel):
        """禁用指定级别的降级策略"""
        with self._lock:
            if level in self._strategies:
                self._strategies[level].enabled = False
                logger.info(f"降级策略已禁用: {level.value}")

    def is_strategy_enabled(self, level: DegradationLevel) -> bool:
        """检查指定级别的降级策略是否启用"""
        with self._lock:
            strategy = self._strategies.get(level)
            return strategy.enabled if strategy else False
