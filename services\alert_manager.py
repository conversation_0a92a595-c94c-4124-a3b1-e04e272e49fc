"""
告警管理器
实现智能告警机制，支持多种告警规则、通知方式和告警抑制策略
"""

import asyncio
import json
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any

from core.logging.logging_system import log as logger
from core.metrics_collector import metrics_collector


class AlertSeverity(Enum):
    """告警严重程度"""
    CRITICAL = "critical"  # 严重
    HIGH = "high"  # 高
    MEDIUM = "medium"  # 中等
    LOW = "low"  # 低
    INFO = "info"  # 信息


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"  # 活跃
    RESOLVED = "resolved"  # 已解决
    SUPPRESSED = "suppressed"  # 已抑制
    ACKNOWLEDGED = "acknowledged"  # 已确认


class ComparisonOperator(Enum):
    """比较操作符"""
    GT = ">"  # 大于
    GTE = ">="  # 大于等于
    LT = "<"  # 小于
    LTE = "<="  # 小于等于
    EQ = "=="  # 等于
    NEQ = "!="  # 不等于


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    operator: ComparisonOperator
    threshold: int | float
    severity: AlertSeverity
    description: str = ""
    duration_seconds: int = 60  # 持续时间阈值
    evaluation_interval: int = 30  # 评估间隔
    labels: dict[str, str] = field(default_factory=dict)
    enabled: bool = True


@dataclass
class Alert:
    """告警实例"""
    rule_name: str
    metric_name: str
    current_value: int | float
    threshold: int | float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    created_at: float
    updated_at: float
    resolved_at: float | None = None
    acknowledged_at: float | None = None
    labels: dict[str, str] = field(default_factory=dict)
    alert_id: str = ""

    def __post_init__(self):
        if not self.alert_id:
            self.alert_id = f"{self.rule_name}_{int(self.created_at)}"


@dataclass
class NotificationChannel:
    """通知渠道"""
    name: str
    channel_type: str  # email, webhook, log, console
    config: dict[str, Any]
    enabled: bool = True
    severity_filter: list[AlertSeverity] = field(default_factory=list)


class AlertManager:
    """
    告警管理器
    
    负责告警规则管理、告警评估、通知发送和告警生命周期管理
    """

    def __init__(self, config_file: str = "config/alert_rules.json"):
        """
        初始化告警管理器
        
        Args:
            config_file: 告警规则配置文件路径
        """
        self.config_file = Path(config_file)
        self.rules: dict[str, AlertRule] = {}
        self.active_alerts: dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.notification_channels: dict[str, NotificationChannel] = {}
        self.suppression_rules: dict[str, dict[str, Any]] = {}

        # 告警状态跟踪
        self.rule_states: dict[str, dict[str, Any]] = defaultdict(dict)

        # 运行状态
        self.is_running = False
        self.evaluation_task: asyncio.Task | None = None

        # 加载配置
        self._load_configuration()
        self._setup_default_rules()
        self._setup_default_channels()

        logger.info(f"AlertManager initialized with {len(self.rules)} rules")

    def _load_configuration(self):
        """加载告警配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self._parse_config(config)
                logger.info(f"Alert configuration loaded from {self.config_file}")
            else:
                logger.info("No alert configuration file found, using defaults")
        except Exception as e:
            logger.error(f"Failed to load alert configuration: {e}")

    def _parse_config(self, config: dict[str, Any]):
        """解析配置文件"""
        # 解析告警规则
        for rule_data in config.get("rules", []):
            rule = AlertRule(
                name=rule_data["name"],
                metric_name=rule_data["metric_name"],
                operator=ComparisonOperator(rule_data["operator"]),
                threshold=rule_data["threshold"],
                severity=AlertSeverity(rule_data["severity"]),
                description=rule_data.get("description", ""),
                duration_seconds=rule_data.get("duration_seconds", 60),
                evaluation_interval=rule_data.get("evaluation_interval", 30),
                labels=rule_data.get("labels", {}),
                enabled=rule_data.get("enabled", True)
            )
            self.rules[rule.name] = rule

        # 解析通知渠道
        for channel_data in config.get("notification_channels", []):
            channel = NotificationChannel(
                name=channel_data["name"],
                channel_type=channel_data["type"],
                config=channel_data["config"],
                enabled=channel_data.get("enabled", True),
                severity_filter=[AlertSeverity(s) for s in channel_data.get("severity_filter", [])]
            )
            self.notification_channels[channel.name] = channel

    def _setup_default_rules(self):
        """设置默认告警规则"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                metric_name="system.cpu.usage",
                operator=ComparisonOperator.GT,
                threshold=80.0,
                severity=AlertSeverity.HIGH,
                description="CPU使用率过高",
                duration_seconds=120
            ),
            AlertRule(
                name="high_memory_usage",
                metric_name="system.memory.usage",
                operator=ComparisonOperator.GT,
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                description="内存使用率过高",
                duration_seconds=120
            ),
            AlertRule(
                name="low_memory_available",
                metric_name="system.memory.available",
                operator=ComparisonOperator.LT,
                threshold=500,  # MB
                severity=AlertSeverity.MEDIUM,
                description="可用内存不足",
                duration_seconds=60
            ),
            AlertRule(
                name="high_error_rate",
                metric_name="errors.rate",
                operator=ComparisonOperator.GT,
                threshold=0.05,  # 5%
                severity=AlertSeverity.CRITICAL,
                description="错误率过高",
                duration_seconds=30
            ),
            AlertRule(
                name="registration_task_failure",
                metric_name="registration.tasks.success.rate",
                operator=ComparisonOperator.LT,
                threshold=0.9,  # 90%
                severity=AlertSeverity.HIGH,
                description="规则注册任务成功率过低",
                duration_seconds=180
            ),
            AlertRule(
                name="degradation_mode_active",
                metric_name="degradation.status",
                operator=ComparisonOperator.GT,
                threshold=0,  # 0=正常, 1=降级, 2=失败
                severity=AlertSeverity.MEDIUM,
                description="系统进入降级模式",
                duration_seconds=30
            ),
            AlertRule(
                name="circuit_breaker_open",
                metric_name="degradation.circuit.breaker.state",
                operator=ComparisonOperator.EQ,
                threshold=2,  # 0=关闭, 1=半开, 2=开启
                severity=AlertSeverity.HIGH,
                description="熔断器开启",
                duration_seconds=0  # 立即告警
            ),
        ]

        for rule in default_rules:
            if rule.name not in self.rules:
                self.rules[rule.name] = rule

    def _setup_default_channels(self):
        """设置默认通知渠道"""
        default_channels = [
            NotificationChannel(
                name="console",
                channel_type="console",
                config={},
                enabled=True
            ),
            NotificationChannel(
                name="log",
                channel_type="log",
                config={"log_level": "WARNING"},
                enabled=True
            ),
        ]

        for channel in default_channels:
            if channel.name not in self.notification_channels:
                self.notification_channels[channel.name] = channel

    async def start(self):
        """启动告警管理器"""
        if self.is_running:
            logger.warning("AlertManager is already running")
            return

        self.is_running = True
        self.evaluation_task = asyncio.create_task(self._evaluation_loop())
        logger.info("AlertManager started")

    async def stop(self):
        """停止告警管理器"""
        if not self.is_running:
            return

        self.is_running = False
        if self.evaluation_task:
            self.evaluation_task.cancel()
            try:
                await self.evaluation_task
            except asyncio.CancelledError:
                pass

        logger.info("AlertManager stopped")

    async def _evaluation_loop(self):
        """告警评估循环"""
        while self.is_running:
            try:
                await self._evaluate_all_rules()
                await asyncio.sleep(10)  # 每10秒评估一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert evaluation loop: {e}")
                await asyncio.sleep(10)

    async def _evaluate_all_rules(self):
        """评估所有告警规则"""
        current_time = time.time()

        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue

            # 检查评估间隔
            last_evaluation = self.rule_states[rule_name].get("last_evaluation", 0)
            if current_time - last_evaluation < rule.evaluation_interval:
                continue

            self.rule_states[rule_name]["last_evaluation"] = current_time

            try:
                await self._evaluate_rule(rule)
            except Exception as e:
                logger.error(f"Error evaluating rule {rule_name}: {e}")

    async def _evaluate_rule(self, rule: AlertRule):
        """评估单个告警规则"""
        # 获取指标当前值
        current_value = metrics_collector.get_metric_current_value(rule.metric_name)
        if current_value is None:
            return

        # 评估条件
        condition_met = self._evaluate_condition(current_value, rule.operator, rule.threshold)

        rule_state = self.rule_states[rule.name]
        current_time = time.time()

        if condition_met:
            # 条件满足
            if "first_trigger_time" not in rule_state:
                rule_state["first_trigger_time"] = current_time

            # 检查持续时间
            trigger_duration = current_time - rule_state["first_trigger_time"]
            if trigger_duration >= rule.duration_seconds:
                # 触发告警
                await self._trigger_alert(rule, current_value)
        else:
            # 条件不满足，重置状态
            if "first_trigger_time" in rule_state:
                del rule_state["first_trigger_time"]

            # 解决告警
            await self._resolve_alert(rule.name)

    def _evaluate_condition(self, value: int | float, operator: ComparisonOperator, 
                          threshold: int | float) -> bool:
        """评估条件"""
        if operator == ComparisonOperator.GT:
            return value > threshold
        elif operator == ComparisonOperator.GTE:
            return value >= threshold
        elif operator == ComparisonOperator.LT:
            return value < threshold
        elif operator == ComparisonOperator.LTE:
            return value <= threshold
        elif operator == ComparisonOperator.EQ:
            return value == threshold
        elif operator == ComparisonOperator.NEQ:
            return value != threshold
        return False

    async def _trigger_alert(self, rule: AlertRule, current_value: int | float):
        """触发告警"""
        alert_id = f"{rule.name}_{int(time.time())}"

        # 检查是否已存在活跃告警
        existing_alert = None
        for alert in self.active_alerts.values():
            if alert.rule_name == rule.name and alert.status == AlertStatus.ACTIVE:
                existing_alert = alert
                break

        if existing_alert:
            # 更新现有告警
            existing_alert.current_value = current_value
            existing_alert.updated_at = time.time()
        else:
            # 创建新告警
            message = f"{rule.description}: {rule.metric_name}={current_value} {rule.operator.value} {rule.threshold}"

            alert = Alert(
                rule_name=rule.name,
                metric_name=rule.metric_name,
                current_value=current_value,
                threshold=rule.threshold,
                severity=rule.severity,
                status=AlertStatus.ACTIVE,
                message=message,
                created_at=time.time(),
                updated_at=time.time(),
                labels=rule.labels.copy(),
                alert_id=alert_id
            )

            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)

            # 发送通知
            await self._send_notifications(alert)

            logger.warning(f"Alert triggered: {alert.message}")

    async def _resolve_alert(self, rule_name: str):
        """解决告警"""
        resolved_alerts = []

        for alert_id, alert in list(self.active_alerts.items()):
            if alert.rule_name == rule_name and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = time.time()
                alert.updated_at = time.time()

                resolved_alerts.append(alert)
                del self.active_alerts[alert_id]

        for alert in resolved_alerts:
            await self._send_notifications(alert)
            logger.info(f"Alert resolved: {alert.rule_name}")

    async def _send_notifications(self, alert: Alert):
        """发送告警通知"""
        for channel_name, channel in self.notification_channels.items():
            if not channel.enabled:
                continue

            # 检查严重程度过滤
            if channel.severity_filter and alert.severity not in channel.severity_filter:
                continue

            try:
                await self._send_notification(channel, alert)
            except Exception as e:
                logger.error(f"Failed to send notification via {channel_name}: {e}")

    async def _send_notification(self, channel: NotificationChannel, alert: Alert):
        """发送单个通知"""
        if channel.channel_type == "console":
            print(f"🚨 ALERT [{alert.severity.value.upper()}]: {alert.message}")

        elif channel.channel_type == "log":
            log_level = channel.config.get("log_level", "WARNING")
            if log_level == "ERROR":
                logger.error(f"Alert: {alert.message}")
            elif log_level == "WARNING":
                logger.warning(f"Alert: {alert.message}")
            else:
                logger.info(f"Alert: {alert.message}")

        elif channel.channel_type == "webhook":
            # 这里可以实现webhook通知
            webhook_url = channel.config.get("url")
            if webhook_url:
                logger.info(f"Would send webhook to {webhook_url}: {alert.message}")

        elif channel.channel_type == "email":
            # 这里可以实现邮件通知
            email_to = channel.config.get("to")
            if email_to:
                logger.info(f"Would send email to {email_to}: {alert.message}")

    def get_active_alerts(self) -> list[Alert]:
        """获取活跃告警列表"""
        return list(self.active_alerts.values())

    def get_alert_history(self, limit: int = 100) -> list[Alert]:
        """获取告警历史"""
        history = list(self.alert_history)
        return history[-limit:] if limit else history

    def get_alert_statistics(self) -> dict[str, Any]:
        """获取告警统计信息"""
        active_count = len(self.active_alerts)
        total_count = len(self.alert_history)

        # 按严重程度统计
        severity_stats = defaultdict(int)
        for alert in self.active_alerts.values():
            severity_stats[alert.severity.value] += 1

        # 按规则统计
        rule_stats = defaultdict(int)
        for alert in self.alert_history:
            rule_stats[alert.rule_name] += 1

        return {
            "active_alerts": active_count,
            "total_alerts": total_count,
            "severity_distribution": dict(severity_stats),
            "rule_distribution": dict(rule_stats),
            "rules_count": len(self.rules),
            "channels_count": len(self.notification_channels)
        }


# 全局告警管理器实例
alert_manager = AlertManager()
